import { useState } from 'react';
import './Dashboard.css';

function AdminDashboard() {
    const [activeTab, setActiveTab] = useState('expenses');
    const [expenses, setExpenses] = useState([
        {
            id: 1,
            employee: '<PERSON>',
            amount: 1200,
            category: 'Travel',
            description: 'Flight tickets for business trip',
            date: '2024-01-15',
            status: 'Pending',
            receipt: 'receipt_001.pdf'
        },
        {
            id: 2,
            employee: '<PERSON>',
            amount: 850,
            category: 'Meals',
            description: 'Client dinner meeting',
            date: '2024-01-14',
            status: 'Approved',
            receipt: null
        },
        {
            id: 3,
            employee: '<PERSON>',
            amount: 2500,
            category: 'Equipment',
            description: 'New laptop for development',
            date: '2024-01-12',
            status: 'Pending',
            receipt: 'receipt_003.pdf'
        }
    ]);

    const [users] = useState([
        { id: 1, name: '<PERSON>', email: '<EMAIL>', role: 'Employee', department: 'Sales' },
        { id: 2, name: '<PERSON>', email: '<EMAIL>', role: 'Employee', department: 'Marketing' },
        { id: 3, name: '<PERSON>', email: '<EMAIL>', role: 'Employee', department: 'Development' },
        { id: 4, name: '<PERSON> <PERSON>', email: '<EMAIL>', role: 'Finance', department: 'Finance' }
    ]);

    const [categories, setCategories] = useState([
        { id: 1, name: 'Travel', active: true, count: 15 },
        { id: 2, name: 'Meals', active: true, count: 8 },
        { id: 3, name: 'Equipment', active: true, count: 12 },
        { id: 4, name: 'Office Supplies', active: true, count: 5 },
        { id: 5, name: 'Training', active: false, count: 2 }
    ]);

    const [filterStatus, setFilterStatus] = useState('All');
    const [selectedExpense, setSelectedExpense] = useState(null);
    const [rejectReason, setRejectReason] = useState('');

    const handleApprove = (expenseId) => {
        setExpenses(expenses.map(exp => 
            exp.id === expenseId ? { ...exp, status: 'Approved' } : exp
        ));
        alert('Expense approved successfully!');
    };

    const handleReject = (expenseId) => {
        if (!rejectReason.trim()) {
            alert('Please provide a reason for rejection');
            return;
        }
        setExpenses(expenses.map(exp => 
            exp.id === expenseId ? { ...exp, status: 'Rejected', rejectReason } : exp
        ));
        setSelectedExpense(null);
        setRejectReason('');
        alert('Expense rejected successfully!');
    };

    const getStatusColor = (status) => {
        switch (status) {
            case 'Approved': return 'var(--accent-tertiary)';
            case 'Rejected': return 'var(--accent-secondary)';
            case 'Pending': return 'var(--text-secondary)';
            default: return 'var(--text-secondary)';
        }
    };

    const filteredExpenses = filterStatus === 'All' 
        ? expenses 
        : expenses.filter(exp => exp.status === filterStatus);

    const tabs = [
        { id: 'expenses', label: 'All Expenses', icon: '📋' },
        { id: 'users', label: 'User Management', icon: '👥' },
        { id: 'categories', label: 'Categories', icon: '🏷️' },
        { id: 'audit', label: 'Audit Trail', icon: '🔍' }
    ];

    return (
        <div className="dashboard-container">
            <div className="dashboard-background">
                <div className="dashboard-particles"></div>
            </div>

            <div className="dashboard-content">
                <div className="dashboard-header">
                    <h1 className="dashboard-title">
                        Admin <span className="gradient-text">Dashboard</span>
                    </h1>
                    <p className="dashboard-subtitle">Manage expenses, users, and system settings</p>
                </div>

                <div className="dashboard-stats">
                    <div className="stat-card">
                        <div className="stat-icon">📊</div>
                        <div className="stat-info">
                            <span className="stat-value">24</span>
                            <span className="stat-label">Total Expenses</span>
                        </div>
                    </div>
                    <div className="stat-card">
                        <div className="stat-icon">⏳</div>
                        <div className="stat-info">
                            <span className="stat-value">8</span>
                            <span className="stat-label">Pending Review</span>
                        </div>
                    </div>
                    <div className="stat-card">
                        <div className="stat-icon">✅</div>
                        <div className="stat-info">
                            <span className="stat-value">12</span>
                            <span className="stat-label">Approved</span>
                        </div>
                    </div>
                    <div className="stat-card">
                        <div className="stat-icon">💰</div>
                        <div className="stat-info">
                            <span className="stat-value">₹45,200</span>
                            <span className="stat-label">Total Amount</span>
                        </div>
                    </div>
                </div>

                <div className="dashboard-tabs">
                    {tabs.map(tab => (
                        <button
                            key={tab.id}
                            className={`tab-btn ${activeTab === tab.id ? 'active' : ''}`}
                            onClick={() => setActiveTab(tab.id)}
                        >
                            <span className="tab-icon">{tab.icon}</span>
                            {tab.label}
                        </button>
                    ))}
                </div>

                <div className="dashboard-main">
                    {activeTab === 'expenses' && (
                        <div className="tab-content">
                            <div className="content-card">
                                <div className="card-header">
                                    <h2 className="card-title">All Submitted Expenses</h2>
                                    <div className="filter-controls">
                                        <select 
                                            className="filter-select"
                                            value={filterStatus}
                                            onChange={(e) => setFilterStatus(e.target.value)}
                                        >
                                            <option value="All">All Status</option>
                                            <option value="Pending">Pending</option>
                                            <option value="Approved">Approved</option>
                                            <option value="Rejected">Rejected</option>
                                        </select>
                                    </div>
                                </div>
                                <div className="expenses-table">
                                    <div className="table-header">
                                        <span>Employee</span>
                                        <span>Amount</span>
                                        <span>Category</span>
                                        <span>Date</span>
                                        <span>Status</span>
                                        <span>Actions</span>
                                    </div>
                                    {filteredExpenses.map(expense => (
                                        <div key={expense.id} className="table-row">
                                            <span className="employee-name">{expense.employee}</span>
                                            <span className="expense-amount">₹{expense.amount}</span>
                                            <span className="expense-category">{expense.category}</span>
                                            <span className="expense-date">{expense.date}</span>
                                            <span 
                                                className="expense-status"
                                                style={{ color: getStatusColor(expense.status) }}
                                            >
                                                {expense.status}
                                            </span>
                                            <div className="table-actions">
                                                {expense.status === 'Pending' && (
                                                    <>
                                                        <button 
                                                            className="action-btn approve-btn"
                                                            onClick={() => handleApprove(expense.id)}
                                                        >
                                                            ✅ Approve
                                                        </button>
                                                        <button 
                                                            className="action-btn reject-btn"
                                                            onClick={() => setSelectedExpense(expense)}
                                                        >
                                                            ❌ Reject
                                                        </button>
                                                    </>
                                                )}
                                                {expense.receipt && (
                                                    <button className="action-btn view-btn">📄 View Receipt</button>
                                                )}
                                                <button className="action-btn details-btn">👁️ Details</button>
                                            </div>
                                        </div>
                                    ))}
                                </div>
                            </div>
                        </div>
                    )}

                    {activeTab === 'users' && (
                        <div className="tab-content">
                            <div className="content-card">
                                <h2 className="card-title">User Management</h2>
                                <div className="users-grid">
                                    {users.map(user => (
                                        <div key={user.id} className="user-card">
                                            <div className="user-avatar">👤</div>
                                            <div className="user-info">
                                                <h3 className="user-name">{user.name}</h3>
                                                <p className="user-email">{user.email}</p>
                                                <div className="user-meta">
                                                    <span className="user-role">{user.role}</span>
                                                    <span className="user-department">{user.department}</span>
                                                </div>
                                            </div>
                                            <div className="user-actions">
                                                <button className="action-btn view-btn">👁️ View Profile</button>
                                                <button className="action-btn details-btn">📊 Expenses</button>
                                            </div>
                                        </div>
                                    ))}
                                </div>
                            </div>
                        </div>
                    )}

                    {activeTab === 'categories' && (
                        <div className="tab-content">
                            <div className="content-card">
                                <div className="card-header">
                                    <h2 className="card-title">Expense Categories</h2>
                                    <button className="add-btn">➕ Add Category</button>
                                </div>
                                <div className="categories-list">
                                    {categories.map(category => (
                                        <div key={category.id} className="category-item">
                                            <div className="category-info">
                                                <span className="category-name">{category.name}</span>
                                                <span className="category-count">{category.count} expenses</span>
                                            </div>
                                            <div className="category-status">
                                                <span className={`status-badge ${category.active ? 'active' : 'inactive'}`}>
                                                    {category.active ? 'Active' : 'Inactive'}
                                                </span>
                                            </div>
                                            <div className="category-actions">
                                                <button className="action-btn edit-btn">✏️ Edit</button>
                                                <button className="action-btn toggle-btn">
                                                    {category.active ? '🔒 Deactivate' : '🔓 Activate'}
                                                </button>
                                            </div>
                                        </div>
                                    ))}
                                </div>
                            </div>
                        </div>
                    )}

                    {activeTab === 'audit' && (
                        <div className="tab-content">
                            <div className="content-card">
                                <h2 className="card-title">Audit Trail</h2>
                                <div className="audit-list">
                                    <div className="audit-item">
                                        <div className="audit-icon">✅</div>
                                        <div className="audit-info">
                                            <span className="audit-action">Expense Approved</span>
                                            <span className="audit-details">John Doe's travel expense (₹1,200) approved</span>
                                            <span className="audit-time">2 hours ago</span>
                                        </div>
                                    </div>
                                    <div className="audit-item">
                                        <div className="audit-icon">❌</div>
                                        <div className="audit-info">
                                            <span className="audit-action">Expense Rejected</span>
                                            <span className="audit-details">Jane Smith's meal expense (₹850) rejected - Missing receipt</span>
                                            <span className="audit-time">4 hours ago</span>
                                        </div>
                                    </div>
                                    <div className="audit-item">
                                        <div className="audit-icon">📤</div>
                                        <div className="audit-info">
                                            <span className="audit-action">New Expense Submitted</span>
                                            <span className="audit-details">Mike Johnson submitted equipment expense (₹2,500)</span>
                                            <span className="audit-time">6 hours ago</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    )}
                </div>
            </div>

            {/* Reject Modal */}
            {selectedExpense && (
                <div className="modal-overlay">
                    <div className="modal-content">
                        <h3>Reject Expense</h3>
                        <p>Rejecting expense from {selectedExpense.employee} for ₹{selectedExpense.amount}</p>
                        <textarea
                            className="form-input"
                            placeholder="Reason for rejection..."
                            value={rejectReason}
                            onChange={(e) => setRejectReason(e.target.value)}
                            rows="3"
                        />
                        <div className="modal-actions">
                            <button 
                                className="action-btn reject-btn"
                                onClick={() => handleReject(selectedExpense.id)}
                            >
                                Reject Expense
                            </button>
                            <button 
                                className="action-btn cancel-btn"
                                onClick={() => setSelectedExpense(null)}
                            >
                                Cancel
                            </button>
                        </div>
                    </div>
                </div>
            )}
        </div>
    );
}

export default AdminDashboard;
