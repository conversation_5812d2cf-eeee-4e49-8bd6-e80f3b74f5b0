import './Hero.css';

function Hero() {
    return (
        <section className="hero-section">
            <div className="hero-background">
                <div className="hero-particles"></div>
            </div>
            <div className="container">
                <div className="hero-content">
                    <div className="hero-badge fade-in-up">
                        <span>✨ New Feature: AI-Powered Insights</span>
                    </div>
                    <h1 className="hero-title fade-in-up">
                        Track Every <span className="gradient-text">Rupee</span>
                        <br />
                        <span className="gradient-text">Effortlessly</span>
                    </h1>
                    <p className="hero-subtitle fade-in-up">
                        Transform your financial habits with our intelligent expense tracker.
                        Smart analytics, beautiful insights, and complete privacy.
                    </p>
                    <div className="hero-buttons fade-in-up">
                        <a href="/signup" className="gradient-btn primary-btn">
                            Start Free Trial
                            <span className="btn-icon">→</span>
                        </a>
                        <a href="#features" className="glass-btn">
                            <span className="btn-icon">▶</span>
                            Watch Demo
                        </a>
                    </div>
                    <div className="hero-stats fade-in-up">
                        <div className="stat-item">
                            <span className="stat-number">10K+</span>
                            <span className="stat-label">Active Users</span>
                        </div>
                        <div className="stat-item">
                            <span className="stat-number">₹50L+</span>
                            <span className="stat-label">Money Tracked</span>
                        </div>
                        <div className="stat-item">
                            <span className="stat-number">4.9★</span>
                            <span className="stat-label">User Rating</span>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    );
}

export default Hero;