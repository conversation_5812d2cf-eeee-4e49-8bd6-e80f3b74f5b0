{"ast": null, "code": "var _jsxFileName = \"D:\\\\Expense Tracker App (MERN )\\\\expensetracker\\\\src\\\\App.js\",\n  _s = $RefreshSig$();\nimport { BrowserRouter as Router, Routes, Route, useLocation } from \"react-router-dom\";\nimport { useEffect, useState } from \"react\";\nimport Navbar from \"./components/Navbar\";\nimport Loader from \"./components/Loader\";\nimport Home from \"./pages/Home\";\nimport About from \"./pages/About\";\nimport Contact from \"./pages/Contact\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction AppContent() {\n  _s();\n  const location = useLocation();\n  const [loading, setLoading] = useState(false);\n  useEffect(() => {\n    setLoading(true);\n    const timer = setTimeout(() => {\n      setLoading(false);\n    }, 500); // half a second delay\n\n    return () => clearTimeout(timer);\n  }, [location]);\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(Loader, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 24,\n      columnNumber: 12\n    }, this);\n  }\n}\n_s(AppContent, \"12V4DhK/+FphMBmcfsoKCCzma6g=\", false, function () {\n  return [useLocation];\n});\n_c = AppContent;\nfunction App() {\n  return /*#__PURE__*/_jsxDEV(Router, {\n    children: /*#__PURE__*/_jsxDEV(AppContent, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 32,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 31,\n    columnNumber: 5\n  }, this);\n}\n_c2 = App;\nexport default App;\nvar _c, _c2;\n$RefreshReg$(_c, \"AppContent\");\n$RefreshReg$(_c2, \"App\");", "map": {"version": 3, "names": ["<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Router", "Routes", "Route", "useLocation", "useEffect", "useState", "<PERSON><PERSON><PERSON>", "Loader", "Home", "About", "Contact", "jsxDEV", "_jsxDEV", "A<PERSON><PERSON><PERSON>nt", "_s", "location", "loading", "setLoading", "timer", "setTimeout", "clearTimeout", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "App", "children", "_c2", "$RefreshReg$"], "sources": ["D:/Expense Tracker App (MERN )/expensetracker/src/App.js"], "sourcesContent": ["import { BrowserRouter as Router, Routes, Route, useLocation } from \"react-router-dom\";\nimport { useEffect, useState } from \"react\";\nimport Navbar from \"./components/Navbar\";\nimport Loader from \"./components/Loader\";\nimport Home from \"./pages/Home\";\nimport About from \"./pages/About\";\nimport Contact  from \"./pages/Contact\";\n\nfunction AppContent() {\n  const location = useLocation();\n  const [loading, setLoading] = useState(false);\n\n  useEffect(() => {\n    setLoading(true);\n\n    const timer = setTimeout(() => {\n      setLoading(false);\n    }, 500); // half a second delay\n\n    return () => clearTimeout(timer);\n  }, [location]);\n\n  if (loading) {\n    return <Loader />;\n  }\n  \n}\n\nfunction App() {\n  return (\n    <Router>\n      <AppContent />\n    </Router>\n  );\n}\n\nexport default App;\n"], "mappings": ";;AAAA,SAASA,aAAa,IAAIC,MAAM,EAAEC,MAAM,EAAEC,KAAK,EAAEC,WAAW,QAAQ,kBAAkB;AACtF,SAASC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAC3C,OAAOC,MAAM,MAAM,qBAAqB;AACxC,OAAOC,MAAM,MAAM,qBAAqB;AACxC,OAAOC,IAAI,MAAM,cAAc;AAC/B,OAAOC,KAAK,MAAM,eAAe;AACjC,OAAOC,OAAO,MAAO,iBAAiB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEvC,SAASC,UAAUA,CAAA,EAAG;EAAAC,EAAA;EACpB,MAAMC,QAAQ,GAAGZ,WAAW,CAAC,CAAC;EAC9B,MAAM,CAACa,OAAO,EAAEC,UAAU,CAAC,GAAGZ,QAAQ,CAAC,KAAK,CAAC;EAE7CD,SAAS,CAAC,MAAM;IACda,UAAU,CAAC,IAAI,CAAC;IAEhB,MAAMC,KAAK,GAAGC,UAAU,CAAC,MAAM;MAC7BF,UAAU,CAAC,KAAK,CAAC;IACnB,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC;;IAET,OAAO,MAAMG,YAAY,CAACF,KAAK,CAAC;EAClC,CAAC,EAAE,CAACH,QAAQ,CAAC,CAAC;EAEd,IAAIC,OAAO,EAAE;IACX,oBAAOJ,OAAA,CAACL,MAAM;MAAAc,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EACnB;AAEF;AAACV,EAAA,CAlBQD,UAAU;EAAA,QACAV,WAAW;AAAA;AAAAsB,EAAA,GADrBZ,UAAU;AAoBnB,SAASa,GAAGA,CAAA,EAAG;EACb,oBACEd,OAAA,CAACZ,MAAM;IAAA2B,QAAA,eACLf,OAAA,CAACC,UAAU;MAAAQ,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACR,CAAC;AAEb;AAACI,GAAA,GANQF,GAAG;AAQZ,eAAeA,GAAG;AAAC,IAAAD,EAAA,EAAAG,GAAA;AAAAC,YAAA,CAAAJ,EAAA;AAAAI,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}