{"ast": null, "code": "var _jsxFileName = \"D:\\\\Expense Tracker App (MERN )\\\\expensetracker\\\\src\\\\components\\\\home\\\\HowItWorks.js\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction HowItWorks() {\n  return /*#__PURE__*/_jsxDEV(\"section\", {\n    className: \"container py-5\",\n    children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n      className: \"text-center text-info mb-4\",\n      children: \"How It Works\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 4,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"row\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"col-md-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"h5\", {\n          children: \"1. Create Account\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 7,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"Sign up using your email and get started instantly.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 8,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 6,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"col-md-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"h5\", {\n          children: \"2. Add Your Expenses\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 11,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"Quickly log transactions with categories and notes.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 12,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 10,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"col-md-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"h5\", {\n          children: \"3. View Reports\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 15,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"Track how you spend, save, and budget better.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 16,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 14,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 5,\n      columnNumber: 13\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 3,\n    columnNumber: 9\n  }, this);\n}\n_c = HowItWorks;\nexport default HowItWorks;\nvar _c;\n$RefreshReg$(_c, \"HowItWorks\");", "map": {"version": 3, "names": ["HowItWorks", "_jsxDEV", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["D:/Expense Tracker App (MERN )/expensetracker/src/components/home/<USER>"], "sourcesContent": ["function HowItWorks() {\r\n    return (\r\n        <section className=\"container py-5\">\r\n            <h2 className=\"text-center text-info mb-4\">How It Works</h2>\r\n            <div className=\"row\">\r\n                <div className=\"col-md-4\">\r\n                    <h5>1. Create Account</h5>\r\n                    <p>Sign up using your email and get started instantly.</p>\r\n                </div>\r\n                <div className=\"col-md-4\">\r\n                    <h5>2. Add Your Expenses</h5>\r\n                    <p>Quickly log transactions with categories and notes.</p>\r\n                </div>\r\n                <div className=\"col-md-4\">\r\n                    <h5>3. View Reports</h5>\r\n                    <p>Track how you spend, save, and budget better.</p>\r\n                </div>\r\n            </div>\r\n        </section>\r\n    );\r\n}\r\nexport default HowItWorks;\r\n"], "mappings": ";;AAAA,SAASA,UAAUA,CAAA,EAAG;EAClB,oBACIC,OAAA;IAASC,SAAS,EAAC,gBAAgB;IAAAC,QAAA,gBAC/BF,OAAA;MAAIC,SAAS,EAAC,4BAA4B;MAAAC,QAAA,EAAC;IAAY;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,eAC5DN,OAAA;MAAKC,SAAS,EAAC,KAAK;MAAAC,QAAA,gBAChBF,OAAA;QAAKC,SAAS,EAAC,UAAU;QAAAC,QAAA,gBACrBF,OAAA;UAAAE,QAAA,EAAI;QAAiB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC1BN,OAAA;UAAAE,QAAA,EAAG;QAAmD;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACzD,CAAC,eACNN,OAAA;QAAKC,SAAS,EAAC,UAAU;QAAAC,QAAA,gBACrBF,OAAA;UAAAE,QAAA,EAAI;QAAoB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC7BN,OAAA;UAAAE,QAAA,EAAG;QAAmD;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACzD,CAAC,eACNN,OAAA;QAAKC,SAAS,EAAC,UAAU;QAAAC,QAAA,gBACrBF,OAAA;UAAAE,QAAA,EAAI;QAAe;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACxBN,OAAA;UAAAE,QAAA,EAAG;QAA6C;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnD,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACD,CAAC;AAElB;AAACC,EAAA,GApBQR,UAAU;AAqBnB,eAAeA,UAAU;AAAC,IAAAQ,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}