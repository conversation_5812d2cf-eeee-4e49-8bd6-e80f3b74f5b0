import './About.css';

function About() {
  const teamMembers = [
    {
      name: "<PERSON>",
      role: "CEO & Founder",
      avatar: "👩‍💼",
      description: "10+ years in fintech, passionate about making financial management accessible to everyone."
    },
    {
      name: "<PERSON>",
      role: "<PERSON><PERSON>",
      avatar: "👨‍💻",
      description: "Full-stack developer with expertise in secure financial applications and AI integration."
    },
    {
      name: "<PERSON>",
      role: "Head of Design",
      avatar: "👩‍🎨",
      description: "UX/UI designer focused on creating intuitive and beautiful user experiences."
    },
    {
      name: "<PERSON>",
      role: "Lead Developer",
      avatar: "👨‍🔬",
      description: "Backend specialist ensuring your financial data is secure and always available."
    }
  ];

  const features = [
    {
      icon: "🎯",
      title: "Our Mission",
      description: "To empower individuals and businesses with intelligent financial tracking tools that make money management effortless and insightful."
    },
    {
      icon: "👁️",
      title: "Our Vision",
      description: "A world where everyone has complete control and understanding of their financial health through smart, accessible technology."
    },
    {
      icon: "💎",
      title: "Our Values",
      description: "Privacy, security, simplicity, and innovation drive everything we do. Your financial data belongs to you, always."
    }
  ];

  return (
    <div className="about-container">
      <div className="about-background">
        <div className="about-particles"></div>
      </div>

      {/* Hero Section */}
      <section className="about-hero">
        <div className="container">
          <div className="hero-content fade-in-up">
            <h1 className="about-title">
              About <span className="gradient-text">ExpenseTracker</span>
            </h1>
            <p className="about-subtitle">
              We're on a mission to revolutionize how people manage their finances.
              Founded in 2023, ExpenseTracker has helped thousands of users take control
              of their financial future with intelligent, secure, and beautiful tools.
            </p>
          </div>
        </div>
      </section>

      {/* Mission, Vision, Values */}
      <section className="about-features">
        <div className="container">
          <div className="features-grid">
            {features.map((feature, index) => (
              <div key={index} className="feature-card glass-card fade-in-up">
                <div className="feature-icon">{feature.icon}</div>
                <h3 className="feature-title">{feature.title}</h3>
                <p className="feature-description">{feature.description}</p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Story Section */}
      <section className="about-story">
        <div className="container">
          <div className="story-content">
            <div className="story-text fade-in-up">
              <h2 className="section-title">Our Story</h2>
              <p>
                ExpenseTracker was born from a simple frustration: existing expense tracking
                apps were either too complicated or too basic. Our founders, having worked in
                fintech for over a decade, knew there had to be a better way.
              </p>
              <p>
                We started with a vision of creating an expense tracker that would be as
                beautiful as it is powerful, as secure as it is simple. After months of
                research, design, and development, ExpenseTracker was launched with features
                that users actually want and need.
              </p>
              <p>
                Today, we're proud to serve thousands of users worldwide, helping them save
                money, understand their spending patterns, and achieve their financial goals.
                But we're just getting started.
              </p>
            </div>
            <div className="story-stats fade-in-up">
              <div className="stat-item">
                <span className="stat-number">10K+</span>
                <span className="stat-label">Happy Users</span>
              </div>
              <div className="stat-item">
                <span className="stat-number">₹50L+</span>
                <span className="stat-label">Money Tracked</span>
              </div>
              <div className="stat-item">
                <span className="stat-number">99.9%</span>
                <span className="stat-label">Uptime</span>
              </div>
              <div className="stat-item">
                <span className="stat-number">4.9★</span>
                <span className="stat-label">User Rating</span>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Team Section */}
      <section className="about-team">
        <div className="container">
          <div className="section-header">
            <h2 className="section-title fade-in-up">Meet Our Team</h2>
            <p className="section-subtitle fade-in-up">
              The passionate people behind ExpenseTracker
            </p>
          </div>
          <div className="team-grid">
            {teamMembers.map((member, index) => (
              <div key={index} className="team-card glass-card fade-in-up">
                <div className="member-avatar">{member.avatar}</div>
                <h3 className="member-name">{member.name}</h3>
                <p className="member-role">{member.role}</p>
                <p className="member-description">{member.description}</p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="about-cta">
        <div className="container">
          <div className="cta-content fade-in-up">
            <h2 className="cta-title">Ready to Join Our Journey?</h2>
            <p className="cta-subtitle">
              Start tracking your expenses smarter today and become part of our growing community.
            </p>
            <div className="cta-buttons">
              <a href="/signup" className="cta-btn primary-btn">
                Get Started Free
              </a>
              <a href="/contact" className="cta-btn secondary-btn">
                Contact Us
              </a>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
}

export default About;
