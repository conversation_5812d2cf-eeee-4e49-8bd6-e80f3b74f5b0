{"ast": null, "code": "var _jsxFileName = \"D:\\\\Expense Tracker App (MERN )\\\\expensetracker\\\\src\\\\components\\\\home\\\\Features.js\";\nimport './Features.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction Features() {\n  const features = [{\n    icon: \"💰\",\n    title: \"Smart Expense Tracking\",\n    description: \"Effortlessly log expenses with AI-powered categorization and receipt scanning.\",\n    color: \"var(--accent-tertiary)\"\n  }, {\n    icon: \"📊\",\n    title: \"Advanced Analytics\",\n    description: \"Beautiful charts and insights to understand your spending patterns and trends.\",\n    color: \"var(--accent-primary)\"\n  }, {\n    icon: \"🔒\",\n    title: \"Bank-Level Security\",\n    description: \"Your financial data is protected with end-to-end encryption and privacy.\",\n    color: \"var(--accent-secondary)\"\n  }, {\n    icon: \"🎯\",\n    title: \"Goal Setting\",\n    description: \"Set budgets and savings goals with intelligent recommendations and alerts.\",\n    color: \"var(--accent-quaternary)\"\n  }, {\n    icon: \"📱\",\n    title: \"Multi-Platform Sync\",\n    description: \"Access your data seamlessly across all devices with real-time synchronization.\",\n    color: \"var(--accent-tertiary)\"\n  }, {\n    icon: \"🤖\",\n    title: \"AI Insights\",\n    description: \"Get personalized financial advice and spending optimization suggestions.\",\n    color: \"var(--accent-primary)\"\n  }];\n  return /*#__PURE__*/_jsxDEV(\"section\", {\n    id: \"features\",\n    className: \"features-section\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"container\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"features-header\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"section-title fade-in-up\",\n          children: [\"Why Choose \", /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"gradient-text\",\n            children: \"ExpenseTracker\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 48,\n            columnNumber: 36\n          }, this), \"?\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 47,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"section-subtitle fade-in-up\",\n          children: \"Powerful features designed to make expense tracking effortless and insightful\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 50,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 46,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"features-grid\",\n        children: features.map((feature, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"feature-card glass-card fade-in-up\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"feature-icon\",\n            style: {\n              color: feature.color\n            },\n            children: feature.icon\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 58,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"feature-title\",\n            children: feature.title\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 61,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"feature-description\",\n            children: feature.description\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 62,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"feature-hover-effect\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 63,\n            columnNumber: 29\n          }, this)]\n        }, index, true, {\n          fileName: _jsxFileName,\n          lineNumber: 57,\n          columnNumber: 25\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 55,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 45,\n      columnNumber: 13\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 44,\n    columnNumber: 9\n  }, this);\n}\n_c = Features;\nexport default Features;\nvar _c;\n$RefreshReg$(_c, \"Features\");", "map": {"version": 3, "names": ["jsxDEV", "_jsxDEV", "Features", "features", "icon", "title", "description", "color", "id", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "map", "feature", "index", "style", "_c", "$RefreshReg$"], "sources": ["D:/Expense Tracker App (MERN )/expensetracker/src/components/home/<USER>"], "sourcesContent": ["import './Features.css';\r\n\r\nfunction Features() {\r\n    const features = [\r\n        {\r\n            icon: \"💰\",\r\n            title: \"Smart Expense Tracking\",\r\n            description: \"Effortlessly log expenses with AI-powered categorization and receipt scanning.\",\r\n            color: \"var(--accent-tertiary)\"\r\n        },\r\n        {\r\n            icon: \"📊\",\r\n            title: \"Advanced Analytics\",\r\n            description: \"Beautiful charts and insights to understand your spending patterns and trends.\",\r\n            color: \"var(--accent-primary)\"\r\n        },\r\n        {\r\n            icon: \"🔒\",\r\n            title: \"Bank-Level Security\",\r\n            description: \"Your financial data is protected with end-to-end encryption and privacy.\",\r\n            color: \"var(--accent-secondary)\"\r\n        },\r\n        {\r\n            icon: \"🎯\",\r\n            title: \"Goal Setting\",\r\n            description: \"Set budgets and savings goals with intelligent recommendations and alerts.\",\r\n            color: \"var(--accent-quaternary)\"\r\n        },\r\n        {\r\n            icon: \"📱\",\r\n            title: \"Multi-Platform Sync\",\r\n            description: \"Access your data seamlessly across all devices with real-time synchronization.\",\r\n            color: \"var(--accent-tertiary)\"\r\n        },\r\n        {\r\n            icon: \"🤖\",\r\n            title: \"AI Insights\",\r\n            description: \"Get personalized financial advice and spending optimization suggestions.\",\r\n            color: \"var(--accent-primary)\"\r\n        }\r\n    ];\r\n\r\n    return (\r\n        <section id=\"features\" className=\"features-section\">\r\n            <div className=\"container\">\r\n                <div className=\"features-header\">\r\n                    <h2 className=\"section-title fade-in-up\">\r\n                        Why Choose <span className=\"gradient-text\">ExpenseTracker</span>?\r\n                    </h2>\r\n                    <p className=\"section-subtitle fade-in-up\">\r\n                        Powerful features designed to make expense tracking effortless and insightful\r\n                    </p>\r\n                </div>\r\n\r\n                <div className=\"features-grid\">\r\n                    {features.map((feature, index) => (\r\n                        <div key={index} className=\"feature-card glass-card fade-in-up\">\r\n                            <div className=\"feature-icon\" style={{ color: feature.color }}>\r\n                                {feature.icon}\r\n                            </div>\r\n                            <h3 className=\"feature-title\">{feature.title}</h3>\r\n                            <p className=\"feature-description\">{feature.description}</p>\r\n                            <div className=\"feature-hover-effect\"></div>\r\n                        </div>\r\n                    ))}\r\n                </div>\r\n            </div>\r\n        </section>\r\n    );\r\n}\r\n\r\nexport default Features;"], "mappings": ";AAAA,OAAO,gBAAgB;AAAC,SAAAA,MAAA,IAAAC,OAAA;AAExB,SAASC,QAAQA,CAAA,EAAG;EAChB,MAAMC,QAAQ,GAAG,CACb;IACIC,IAAI,EAAE,IAAI;IACVC,KAAK,EAAE,wBAAwB;IAC/BC,WAAW,EAAE,gFAAgF;IAC7FC,KAAK,EAAE;EACX,CAAC,EACD;IACIH,IAAI,EAAE,IAAI;IACVC,KAAK,EAAE,oBAAoB;IAC3BC,WAAW,EAAE,gFAAgF;IAC7FC,KAAK,EAAE;EACX,CAAC,EACD;IACIH,IAAI,EAAE,IAAI;IACVC,KAAK,EAAE,qBAAqB;IAC5BC,WAAW,EAAE,0EAA0E;IACvFC,KAAK,EAAE;EACX,CAAC,EACD;IACIH,IAAI,EAAE,IAAI;IACVC,KAAK,EAAE,cAAc;IACrBC,WAAW,EAAE,4EAA4E;IACzFC,KAAK,EAAE;EACX,CAAC,EACD;IACIH,IAAI,EAAE,IAAI;IACVC,KAAK,EAAE,qBAAqB;IAC5BC,WAAW,EAAE,gFAAgF;IAC7FC,KAAK,EAAE;EACX,CAAC,EACD;IACIH,IAAI,EAAE,IAAI;IACVC,KAAK,EAAE,aAAa;IACpBC,WAAW,EAAE,0EAA0E;IACvFC,KAAK,EAAE;EACX,CAAC,CACJ;EAED,oBACIN,OAAA;IAASO,EAAE,EAAC,UAAU;IAACC,SAAS,EAAC,kBAAkB;IAAAC,QAAA,eAC/CT,OAAA;MAAKQ,SAAS,EAAC,WAAW;MAAAC,QAAA,gBACtBT,OAAA;QAAKQ,SAAS,EAAC,iBAAiB;QAAAC,QAAA,gBAC5BT,OAAA;UAAIQ,SAAS,EAAC,0BAA0B;UAAAC,QAAA,GAAC,aAC1B,eAAAT,OAAA;YAAMQ,SAAS,EAAC,eAAe;YAAAC,QAAA,EAAC;UAAc;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,KACpE;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACLb,OAAA;UAAGQ,SAAS,EAAC,6BAA6B;UAAAC,QAAA,EAAC;QAE3C;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENb,OAAA;QAAKQ,SAAS,EAAC,eAAe;QAAAC,QAAA,EACzBP,QAAQ,CAACY,GAAG,CAAC,CAACC,OAAO,EAAEC,KAAK,kBACzBhB,OAAA;UAAiBQ,SAAS,EAAC,oCAAoC;UAAAC,QAAA,gBAC3DT,OAAA;YAAKQ,SAAS,EAAC,cAAc;YAACS,KAAK,EAAE;cAAEX,KAAK,EAAES,OAAO,CAACT;YAAM,CAAE;YAAAG,QAAA,EACzDM,OAAO,CAACZ;UAAI;YAAAO,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACZ,CAAC,eACNb,OAAA;YAAIQ,SAAS,EAAC,eAAe;YAAAC,QAAA,EAAEM,OAAO,CAACX;UAAK;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eAClDb,OAAA;YAAGQ,SAAS,EAAC,qBAAqB;YAAAC,QAAA,EAAEM,OAAO,CAACV;UAAW;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC5Db,OAAA;YAAKQ,SAAS,EAAC;UAAsB;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA,GANtCG,KAAK;UAAAN,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAOV,CACR;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACD,CAAC;AAElB;AAACK,EAAA,GAnEQjB,QAAQ;AAqEjB,eAAeA,QAAQ;AAAC,IAAAiB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}