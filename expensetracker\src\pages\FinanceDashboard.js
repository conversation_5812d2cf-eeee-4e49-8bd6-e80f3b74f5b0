import { useState } from 'react';
import './Dashboard.css';

function FinanceDashboard() {
    const [activeTab, setActiveTab] = useState('approved');
    const [approvedExpenses, setApprovedExpenses] = useState([
        {
            id: 1,
            employee: '<PERSON>',
            amount: 1200,
            category: 'Travel',
            description: 'Flight tickets for business trip',
            date: '2024-01-15',
            approvedDate: '2024-01-16',
            status: 'Approved',
            receipt: 'receipt_001.pdf'
        },
        {
            id: 2,
            employee: '<PERSON>',
            amount: 850,
            category: 'Meals',
            description: 'Client dinner meeting',
            date: '2024-01-14',
            approvedDate: '2024-01-15',
            status: 'Reimbursed',
            receipt: null,
            reimbursedDate: '2024-01-17',
            paymentProof: 'payment_001.pdf'
        },
        {
            id: 3,
            employee: '<PERSON>',
            amount: 2500,
            category: 'Equipment',
            description: 'New laptop for development',
            date: '2024-01-12',
            approvedDate: '2024-01-13',
            status: 'Approved',
            receipt: 'receipt_003.pdf'
        }
    ]);

    const [selectedExpense, setSelectedExpense] = useState(null);
    const [paymentProof, setPaymentProof] = useState(null);
    const [reimbursementDate, setReimbursementDate] = useState('');

    const handleMarkReimbursed = (expenseId) => {
        if (!reimbursementDate) {
            alert('Please select reimbursement date');
            return;
        }
        
        setApprovedExpenses(approvedExpenses.map(exp => 
            exp.id === expenseId ? { 
                ...exp, 
                status: 'Reimbursed', 
                reimbursedDate: reimbursementDate,
                paymentProof: paymentProof?.name || 'payment_proof.pdf'
            } : exp
        ));
        setSelectedExpense(null);
        setPaymentProof(null);
        setReimbursementDate('');
        alert('Expense marked as reimbursed successfully!');
    };

    const generateReport = (type) => {
        alert(`Generating ${type} report...`);
    };

    const getStatusColor = (status) => {
        switch (status) {
            case 'Approved': return 'var(--accent-tertiary)';
            case 'Reimbursed': return 'var(--accent-primary)';
            default: return 'var(--text-secondary)';
        }
    };

    const totalApproved = approvedExpenses
        .filter(exp => exp.status === 'Approved')
        .reduce((sum, exp) => sum + exp.amount, 0);

    const totalReimbursed = approvedExpenses
        .filter(exp => exp.status === 'Reimbursed')
        .reduce((sum, exp) => sum + exp.amount, 0);

    const tabs = [
        { id: 'approved', label: 'Approved Expenses', icon: '✅' },
        { id: 'reimbursed', label: 'Reimbursed', icon: '💰' },
        { id: 'reports', label: 'Reports', icon: '📊' },
        { id: 'receipts', label: 'Receipt Verification', icon: '📄' }
    ];

    return (
        <div className="dashboard-container">
            <div className="dashboard-background">
                <div className="dashboard-particles"></div>
            </div>

            <div className="dashboard-content">
                <div className="dashboard-header">
                    <h1 className="dashboard-title">
                        Finance <span className="gradient-text">Dashboard</span>
                    </h1>
                    <p className="dashboard-subtitle">Process reimbursements and manage financial records</p>
                </div>

                <div className="dashboard-stats">
                    <div className="stat-card">
                        <div className="stat-icon">⏳</div>
                        <div className="stat-info">
                            <span className="stat-value">₹{totalApproved.toLocaleString()}</span>
                            <span className="stat-label">Pending Reimbursement</span>
                        </div>
                    </div>
                    <div className="stat-card">
                        <div className="stat-icon">💰</div>
                        <div className="stat-info">
                            <span className="stat-value">₹{totalReimbursed.toLocaleString()}</span>
                            <span className="stat-label">Total Reimbursed</span>
                        </div>
                    </div>
                    <div className="stat-card">
                        <div className="stat-icon">📊</div>
                        <div className="stat-info">
                            <span className="stat-value">{approvedExpenses.length}</span>
                            <span className="stat-label">Total Expenses</span>
                        </div>
                    </div>
                    <div className="stat-card">
                        <div className="stat-icon">📅</div>
                        <div className="stat-info">
                            <span className="stat-value">Jan 2024</span>
                            <span className="stat-label">Current Period</span>
                        </div>
                    </div>
                </div>

                <div className="dashboard-tabs">
                    {tabs.map(tab => (
                        <button
                            key={tab.id}
                            className={`tab-btn ${activeTab === tab.id ? 'active' : ''}`}
                            onClick={() => setActiveTab(tab.id)}
                        >
                            <span className="tab-icon">{tab.icon}</span>
                            {tab.label}
                        </button>
                    ))}
                </div>

                <div className="dashboard-main">
                    {activeTab === 'approved' && (
                        <div className="tab-content">
                            <div className="content-card">
                                <h2 className="card-title">Approved Expenses Awaiting Reimbursement</h2>
                                <div className="expenses-table">
                                    <div className="table-header">
                                        <span>Employee</span>
                                        <span>Amount</span>
                                        <span>Category</span>
                                        <span>Approved Date</span>
                                        <span>Status</span>
                                        <span>Actions</span>
                                    </div>
                                    {approvedExpenses.filter(exp => exp.status === 'Approved').map(expense => (
                                        <div key={expense.id} className="table-row">
                                            <span className="employee-name">{expense.employee}</span>
                                            <span className="expense-amount">₹{expense.amount}</span>
                                            <span className="expense-category">{expense.category}</span>
                                            <span className="expense-date">{expense.approvedDate}</span>
                                            <span 
                                                className="expense-status"
                                                style={{ color: getStatusColor(expense.status) }}
                                            >
                                                {expense.status}
                                            </span>
                                            <div className="table-actions">
                                                <button 
                                                    className="action-btn reimburse-btn"
                                                    onClick={() => setSelectedExpense(expense)}
                                                >
                                                    💰 Mark Reimbursed
                                                </button>
                                                {expense.receipt && (
                                                    <button className="action-btn view-btn">📄 View Receipt</button>
                                                )}
                                                <button className="action-btn details-btn">👁️ Details</button>
                                            </div>
                                        </div>
                                    ))}
                                </div>
                            </div>
                        </div>
                    )}

                    {activeTab === 'reimbursed' && (
                        <div className="tab-content">
                            <div className="content-card">
                                <h2 className="card-title">Reimbursed Expenses</h2>
                                <div className="reimbursed-list">
                                    {approvedExpenses.filter(exp => exp.status === 'Reimbursed').map(expense => (
                                        <div key={expense.id} className="reimbursed-item">
                                            <div className="reimbursed-info">
                                                <div className="reimbursed-header">
                                                    <span className="employee-name">{expense.employee}</span>
                                                    <span className="reimbursed-amount">₹{expense.amount}</span>
                                                </div>
                                                <div className="reimbursed-details">
                                                    <span className="expense-category">{expense.category}</span>
                                                    <span className="reimbursed-date">Reimbursed: {expense.reimbursedDate}</span>
                                                </div>
                                                <p className="expense-description">{expense.description}</p>
                                            </div>
                                            <div className="reimbursed-actions">
                                                {expense.paymentProof && (
                                                    <button className="action-btn download-btn">📥 Payment Proof</button>
                                                )}
                                                {expense.receipt && (
                                                    <button className="action-btn view-btn">📄 Receipt</button>
                                                )}
                                            </div>
                                        </div>
                                    ))}
                                </div>
                            </div>
                        </div>
                    )}

                    {activeTab === 'reports' && (
                        <div className="tab-content">
                            <div className="content-card">
                                <h2 className="card-title">Generate Reports</h2>
                                <div className="reports-grid">
                                    <div className="report-card">
                                        <div className="report-icon">📊</div>
                                        <h3 className="report-title">Monthly Report</h3>
                                        <p className="report-description">
                                            Generate monthly reimbursement summary by employee and category
                                        </p>
                                        <button 
                                            className="report-btn"
                                            onClick={() => generateReport('Monthly')}
                                        >
                                            📥 Generate Monthly
                                        </button>
                                    </div>
                                    <div className="report-card">
                                        <div className="report-icon">📈</div>
                                        <h3 className="report-title">Quarterly Report</h3>
                                        <p className="report-description">
                                            Comprehensive quarterly analysis with trends and insights
                                        </p>
                                        <button 
                                            className="report-btn"
                                            onClick={() => generateReport('Quarterly')}
                                        >
                                            📥 Generate Quarterly
                                        </button>
                                    </div>
                                    <div className="report-card">
                                        <div className="report-icon">👥</div>
                                        <h3 className="report-title">Employee Report</h3>
                                        <p className="report-description">
                                            Individual employee expense breakdown and patterns
                                        </p>
                                        <button 
                                            className="report-btn"
                                            onClick={() => generateReport('Employee')}
                                        >
                                            📥 Generate by Employee
                                        </button>
                                    </div>
                                    <div className="report-card">
                                        <div className="report-icon">🏷️</div>
                                        <h3 className="report-title">Category Report</h3>
                                        <p className="report-description">
                                            Expense analysis by category with budget comparisons
                                        </p>
                                        <button 
                                            className="report-btn"
                                            onClick={() => generateReport('Category')}
                                        >
                                            📥 Generate by Category
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    )}

                    {activeTab === 'receipts' && (
                        <div className="tab-content">
                            <div className="content-card">
                                <h2 className="card-title">Receipt Verification</h2>
                                <div className="receipts-list">
                                    {approvedExpenses.filter(exp => exp.receipt).map(expense => (
                                        <div key={expense.id} className="receipt-item">
                                            <div className="receipt-info">
                                                <div className="receipt-header">
                                                    <span className="employee-name">{expense.employee}</span>
                                                    <span className="receipt-amount">₹{expense.amount}</span>
                                                </div>
                                                <div className="receipt-details">
                                                    <span className="expense-category">{expense.category}</span>
                                                    <span className="receipt-file">{expense.receipt}</span>
                                                </div>
                                                <p className="expense-description">{expense.description}</p>
                                            </div>
                                            <div className="receipt-actions">
                                                <button className="action-btn download-btn">📥 Download</button>
                                                <button className="action-btn view-btn">👁️ View</button>
                                                <button className="action-btn verify-btn">✅ Verify</button>
                                            </div>
                                        </div>
                                    ))}
                                </div>
                            </div>
                        </div>
                    )}
                </div>
            </div>

            {/* Reimbursement Modal */}
            {selectedExpense && (
                <div className="modal-overlay">
                    <div className="modal-content">
                        <h3>Mark as Reimbursed</h3>
                        <p>Processing reimbursement for {selectedExpense.employee} - ₹{selectedExpense.amount}</p>
                        
                        <div className="form-group">
                            <label className="form-label">Reimbursement Date</label>
                            <input
                                type="date"
                                className="form-input"
                                value={reimbursementDate}
                                onChange={(e) => setReimbursementDate(e.target.value)}
                            />
                        </div>

                        <div className="form-group">
                            <label className="form-label">Payment Proof (Optional)</label>
                            <input
                                type="file"
                                className="form-input file-input"
                                accept=".pdf,.jpg,.jpeg,.png"
                                onChange={(e) => setPaymentProof(e.target.files[0])}
                            />
                            <small className="form-help">Upload bank transfer receipt or payment confirmation</small>
                        </div>

                        <div className="modal-actions">
                            <button 
                                className="action-btn reimburse-btn"
                                onClick={() => handleMarkReimbursed(selectedExpense.id)}
                            >
                                💰 Mark Reimbursed
                            </button>
                            <button 
                                className="action-btn cancel-btn"
                                onClick={() => setSelectedExpense(null)}
                            >
                                Cancel
                            </button>
                        </div>
                    </div>
                </div>
            )}
        </div>
    );
}

export default FinanceDashboard;
