import { useState } from 'react';
import './Contact.css';

function Contact() {
    const [formData, setFormData] = useState({
        name: '',
        email: '',
        subject: '',
        message: ''
    });

    const [errors, setErrors] = useState({});
    const [isLoading, setIsLoading] = useState(false);
    const [isSubmitted, setIsSubmitted] = useState(false);

    const handleChange = (e) => {
        const { name, value } = e.target;
        setFormData(prev => ({
            ...prev,
            [name]: value
        }));
        // Clear error when user starts typing
        if (errors[name]) {
            setErrors(prev => ({
                ...prev,
                [name]: ''
            }));
        }
    };

    const validateForm = () => {
        const newErrors = {};

        if (!formData.name.trim()) {
            newErrors.name = 'Name is required';
        }

        if (!formData.email) {
            newErrors.email = 'Email is required';
        } else if (!/\S+@\S+\.\S+/.test(formData.email)) {
            newErrors.email = 'Email is invalid';
        }

        if (!formData.subject.trim()) {
            newErrors.subject = 'Subject is required';
        }

        if (!formData.message.trim()) {
            newErrors.message = 'Message is required';
        } else if (formData.message.trim().length < 10) {
            newErrors.message = 'Message must be at least 10 characters';
        }

        setErrors(newErrors);
        return Object.keys(newErrors).length === 0;
    };

    const handleSubmit = async (e) => {
        e.preventDefault();

        if (!validateForm()) {
            return;
        }

        setIsLoading(true);

        try {
            // Simulate API call
            await new Promise(resolve => setTimeout(resolve, 2000));
            console.log('Contact form data:', formData);
            setIsSubmitted(true);
            setFormData({ name: '', email: '', subject: '', message: '' });
        } catch (error) {
            console.error('Contact form error:', error);
            alert('Failed to send message. Please try again.');
        } finally {
            setIsLoading(false);
        }
    };

    const contactInfo = [
        {
            icon: "📧",
            title: "Email Us",
            info: "<EMAIL>",
            description: "Get in touch for support or general inquiries"
        },
        {
            icon: "📞",
            title: "Call Us",
            info: "+1 (555) 123-4567",
            description: "Mon-Fri 9AM-6PM EST"
        },
        {
            icon: "📍",
            title: "Visit Us",
            info: "123 Tech Street, Silicon Valley, CA 94000",
            description: "Our headquarters - visitors welcome!"
        },
        {
            icon: "💬",
            title: "Live Chat",
            info: "Available 24/7",
            description: "Instant support through our website"
        }
    ];

    const faqs = [
        {
            question: "How secure is my financial data?",
            answer: "We use bank-level encryption and never store your banking credentials. All data is encrypted both in transit and at rest."
        },
        {
            question: "Can I export my expense data?",
            answer: "Yes! You can export your data in CSV, PDF, or Excel format anytime from your dashboard."
        },
        {
            question: "Is there a mobile app?",
            answer: "Our web app is fully responsive and works great on mobile. Native iOS and Android apps are coming soon!"
        },
        {
            question: "What payment methods do you accept?",
            answer: "We accept all major credit cards, PayPal, and bank transfers. All payments are processed securely."
        }
    ];

    return (
        <div className="contact-container">
            <div className="contact-background">
                <div className="contact-particles"></div>
            </div>

            {/* Hero Section */}
            <section className="contact-hero">
                <div className="container">
                    <div className="hero-content fade-in-up">
                        <h1 className="contact-title">
                            Get In <span className="gradient-text">Touch</span>
                        </h1>
                        <p className="contact-subtitle">
                            Have questions? We'd love to hear from you. Send us a message
                            and we'll respond as soon as possible.
                        </p>
                    </div>
                </div>
            </section>

            {/* Contact Info & Form */}
            <section className="contact-main">
                <div className="container">
                    <div className="contact-grid">
                        {/* Contact Information */}
                        <div className="contact-info">
                            <h2 className="info-title fade-in-up">Contact Information</h2>
                            <p className="info-subtitle fade-in-up">
                                Choose the best way to reach us
                            </p>

                            <div className="contact-methods">
                                {contactInfo.map((item, index) => (
                                    <div key={index} className="contact-method glass-card fade-in-up">
                                        <div className="method-icon">{item.icon}</div>
                                        <div className="method-content">
                                            <h3 className="method-title">{item.title}</h3>
                                            <p className="method-info">{item.info}</p>
                                            <p className="method-description">{item.description}</p>
                                        </div>
                                    </div>
                                ))}
                            </div>
                        </div>

                        {/* Contact Form */}
                        <div className="contact-form-section">
                            <div className="form-card glass-card">
                                {isSubmitted ? (
                                    <div className="success-message fade-in-up">
                                        <div className="success-icon">✅</div>
                                        <h3>Message Sent Successfully!</h3>
                                        <p>Thank you for contacting us. We'll get back to you within 24 hours.</p>
                                        <button
                                            onClick={() => setIsSubmitted(false)}
                                            className="send-another-btn"
                                        >
                                            Send Another Message
                                        </button>
                                    </div>
                                ) : (
                                    <>
                                        <h2 className="form-title">Send us a Message</h2>
                                        <form onSubmit={handleSubmit} className="contact-form">
                                            <div className="form-row">
                                                <div className="form-group">
                                                    <label htmlFor="name" className="form-label">Name</label>
                                                    <input
                                                        type="text"
                                                        id="name"
                                                        name="name"
                                                        value={formData.name}
                                                        onChange={handleChange}
                                                        className={`form-input ${errors.name ? 'error' : ''}`}
                                                        placeholder="Your full name"
                                                    />
                                                    {errors.name && <span className="error-message">{errors.name}</span>}
                                                </div>

                                                <div className="form-group">
                                                    <label htmlFor="email" className="form-label">Email</label>
                                                    <input
                                                        type="email"
                                                        id="email"
                                                        name="email"
                                                        value={formData.email}
                                                        onChange={handleChange}
                                                        className={`form-input ${errors.email ? 'error' : ''}`}
                                                        placeholder="<EMAIL>"
                                                    />
                                                    {errors.email && <span className="error-message">{errors.email}</span>}
                                                </div>
                                            </div>

                                            <div className="form-group">
                                                <label htmlFor="subject" className="form-label">Subject</label>
                                                <input
                                                    type="text"
                                                    id="subject"
                                                    name="subject"
                                                    value={formData.subject}
                                                    onChange={handleChange}
                                                    className={`form-input ${errors.subject ? 'error' : ''}`}
                                                    placeholder="What's this about?"
                                                />
                                                {errors.subject && <span className="error-message">{errors.subject}</span>}
                                            </div>

                                            <div className="form-group">
                                                <label htmlFor="message" className="form-label">Message</label>
                                                <textarea
                                                    id="message"
                                                    name="message"
                                                    value={formData.message}
                                                    onChange={handleChange}
                                                    className={`form-input form-textarea ${errors.message ? 'error' : ''}`}
                                                    placeholder="Tell us more about your inquiry..."
                                                    rows="5"
                                                />
                                                {errors.message && <span className="error-message">{errors.message}</span>}
                                            </div>

                                            <button
                                                type="submit"
                                                className={`submit-btn ${isLoading ? 'loading' : ''}`}
                                                disabled={isLoading}
                                            >
                                                {isLoading ? (
                                                    <>
                                                        <span className="loading-spinner"></span>
                                                        Sending...
                                                    </>
                                                ) : (
                                                    <>
                                                        <span className="btn-icon">📤</span>
                                                        Send Message
                                                    </>
                                                )}
                                            </button>
                                        </form>
                                    </>
                                )}
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            {/* FAQ Section */}
            <section className="contact-faq">
                <div className="container">
                    <div className="section-header">
                        <h2 className="section-title fade-in-up">Frequently Asked Questions</h2>
                        <p className="section-subtitle fade-in-up">
                            Quick answers to common questions
                        </p>
                    </div>

                    <div className="faq-grid">
                        {faqs.map((faq, index) => (
                            <div key={index} className="faq-item glass-card fade-in-up">
                                <h3 className="faq-question">{faq.question}</h3>
                                <p className="faq-answer">{faq.answer}</p>
                            </div>
                        ))}
                    </div>
                </div>
            </section>
        </div>
    );
}

export default Contact;
