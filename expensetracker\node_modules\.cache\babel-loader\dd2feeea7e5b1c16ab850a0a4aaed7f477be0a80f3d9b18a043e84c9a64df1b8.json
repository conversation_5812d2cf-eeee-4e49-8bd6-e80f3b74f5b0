{"ast": null, "code": "var _jsxFileName = \"D:\\\\Expense Tracker App (MERN )\\\\expensetracker\\\\src\\\\components\\\\home\\\\CTA.js\";\nimport './CTA.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction CTA() {\n  return /*#__PURE__*/_jsxDEV(\"section\", {\n    className: \"cta-section\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"cta-background\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"cta-particles\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 7,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 6,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"container\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"cta-content fade-in-up\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"cta-badge\",\n          children: /*#__PURE__*/_jsxDEV(\"span\", {\n            children: \"\\uD83C\\uDF89 Limited Time Offer\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 12,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 11,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"cta-title\",\n          children: [\"Start Managing Your Money\", /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"gradient-text\",\n            children: \" Today\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 16,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 14,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"cta-subtitle\",\n          children: \"Join thousands of users who have already transformed their financial lives. Get started with our free trial - no credit card required!\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 18,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"cta-buttons\",\n          children: [/*#__PURE__*/_jsxDEV(\"a\", {\n            href: \"/signup\",\n            className: \"cta-primary-btn gradient-btn\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"btn-icon\",\n              children: \"\\uD83D\\uDE80\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 25,\n              columnNumber: 29\n            }, this), \"Start Free Trial\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 24,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n            href: \"/contact\",\n            className: \"cta-secondary-btn glass-btn\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"btn-icon\",\n              children: \"\\uD83D\\uDCAC\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 29,\n              columnNumber: 29\n            }, this), \"Talk to Sales\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 28,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 23,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"cta-features\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"feature-item\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"feature-icon\",\n              children: \"\\u2705\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 36,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              children: \"14-day free trial\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 37,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 35,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"feature-item\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"feature-icon\",\n              children: \"\\u2705\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 40,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              children: \"No credit card required\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 41,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 39,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"feature-item\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"feature-icon\",\n              children: \"\\u2705\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 44,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              children: \"Cancel anytime\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 45,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 43,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 34,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"cta-trust\",\n          children: [/*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"trust-text\",\n            children: \"Trusted by 10,000+ users worldwide\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 50,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"trust-badges\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"trust-badge\",\n              children: \"\\uD83D\\uDD12 Bank-level Security\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 52,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"trust-badge\",\n              children: \"\\u2B50 4.9/5 Rating\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 53,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"trust-badge\",\n              children: \"\\uD83C\\uDFC6 Award Winning\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 54,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 51,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 49,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 10,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 9,\n      columnNumber: 13\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 5,\n    columnNumber: 9\n  }, this);\n}\n_c = CTA;\nexport default CTA;\nvar _c;\n$RefreshReg$(_c, \"CTA\");", "map": {"version": 3, "names": ["jsxDEV", "_jsxDEV", "CTA", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "href", "_c", "$RefreshReg$"], "sources": ["D:/Expense Tracker App (MERN )/expensetracker/src/components/home/<USER>"], "sourcesContent": ["import './CTA.css';\r\n\r\nfunction CTA() {\r\n    return (\r\n        <section className=\"cta-section\">\r\n            <div className=\"cta-background\">\r\n                <div className=\"cta-particles\"></div>\r\n            </div>\r\n            <div className=\"container\">\r\n                <div className=\"cta-content fade-in-up\">\r\n                    <div className=\"cta-badge\">\r\n                        <span>🎉 Limited Time Offer</span>\r\n                    </div>\r\n                    <h2 className=\"cta-title\">\r\n                        Start Managing Your Money\r\n                        <span className=\"gradient-text\"> Today</span>\r\n                    </h2>\r\n                    <p className=\"cta-subtitle\">\r\n                        Join thousands of users who have already transformed their financial lives.\r\n                        Get started with our free trial - no credit card required!\r\n                    </p>\r\n\r\n                    <div className=\"cta-buttons\">\r\n                        <a href=\"/signup\" className=\"cta-primary-btn gradient-btn\">\r\n                            <span className=\"btn-icon\">🚀</span>\r\n                            Start Free Trial\r\n                        </a>\r\n                        <a href=\"/contact\" className=\"cta-secondary-btn glass-btn\">\r\n                            <span className=\"btn-icon\">💬</span>\r\n                            Talk to Sales\r\n                        </a>\r\n                    </div>\r\n\r\n                    <div className=\"cta-features\">\r\n                        <div className=\"feature-item\">\r\n                            <span className=\"feature-icon\">✅</span>\r\n                            <span>14-day free trial</span>\r\n                        </div>\r\n                        <div className=\"feature-item\">\r\n                            <span className=\"feature-icon\">✅</span>\r\n                            <span>No credit card required</span>\r\n                        </div>\r\n                        <div className=\"feature-item\">\r\n                            <span className=\"feature-icon\">✅</span>\r\n                            <span>Cancel anytime</span>\r\n                        </div>\r\n                    </div>\r\n\r\n                    <div className=\"cta-trust\">\r\n                        <p className=\"trust-text\">Trusted by 10,000+ users worldwide</p>\r\n                        <div className=\"trust-badges\">\r\n                            <span className=\"trust-badge\">🔒 Bank-level Security</span>\r\n                            <span className=\"trust-badge\">⭐ 4.9/5 Rating</span>\r\n                            <span className=\"trust-badge\">🏆 Award Winning</span>\r\n                        </div>\r\n                    </div>\r\n                </div>\r\n            </div>\r\n        </section>\r\n    );\r\n}\r\n\r\nexport default CTA;"], "mappings": ";AAAA,OAAO,WAAW;AAAC,SAAAA,MAAA,IAAAC,OAAA;AAEnB,SAASC,GAAGA,CAAA,EAAG;EACX,oBACID,OAAA;IAASE,SAAS,EAAC,aAAa;IAAAC,QAAA,gBAC5BH,OAAA;MAAKE,SAAS,EAAC,gBAAgB;MAAAC,QAAA,eAC3BH,OAAA;QAAKE,SAAS,EAAC;MAAe;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACpC,CAAC,eACNP,OAAA;MAAKE,SAAS,EAAC,WAAW;MAAAC,QAAA,eACtBH,OAAA;QAAKE,SAAS,EAAC,wBAAwB;QAAAC,QAAA,gBACnCH,OAAA;UAAKE,SAAS,EAAC,WAAW;UAAAC,QAAA,eACtBH,OAAA;YAAAG,QAAA,EAAM;UAAqB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjC,CAAC,eACNP,OAAA;UAAIE,SAAS,EAAC,WAAW;UAAAC,QAAA,GAAC,2BAEtB,eAAAH,OAAA;YAAME,SAAS,EAAC,eAAe;YAAAC,QAAA,EAAC;UAAM;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7C,CAAC,eACLP,OAAA;UAAGE,SAAS,EAAC,cAAc;UAAAC,QAAA,EAAC;QAG5B;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eAEJP,OAAA;UAAKE,SAAS,EAAC,aAAa;UAAAC,QAAA,gBACxBH,OAAA;YAAGQ,IAAI,EAAC,SAAS;YAACN,SAAS,EAAC,8BAA8B;YAAAC,QAAA,gBACtDH,OAAA;cAAME,SAAS,EAAC,UAAU;cAAAC,QAAA,EAAC;YAAE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,oBAExC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eACJP,OAAA;YAAGQ,IAAI,EAAC,UAAU;YAACN,SAAS,EAAC,6BAA6B;YAAAC,QAAA,gBACtDH,OAAA;cAAME,SAAS,EAAC,UAAU;cAAAC,QAAA,EAAC;YAAE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,iBAExC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENP,OAAA;UAAKE,SAAS,EAAC,cAAc;UAAAC,QAAA,gBACzBH,OAAA;YAAKE,SAAS,EAAC,cAAc;YAAAC,QAAA,gBACzBH,OAAA;cAAME,SAAS,EAAC,cAAc;cAAAC,QAAA,EAAC;YAAC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACvCP,OAAA;cAAAG,QAAA,EAAM;YAAiB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7B,CAAC,eACNP,OAAA;YAAKE,SAAS,EAAC,cAAc;YAAAC,QAAA,gBACzBH,OAAA;cAAME,SAAS,EAAC,cAAc;cAAAC,QAAA,EAAC;YAAC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACvCP,OAAA;cAAAG,QAAA,EAAM;YAAuB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnC,CAAC,eACNP,OAAA;YAAKE,SAAS,EAAC,cAAc;YAAAC,QAAA,gBACzBH,OAAA;cAAME,SAAS,EAAC,cAAc;cAAAC,QAAA,EAAC;YAAC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACvCP,OAAA;cAAAG,QAAA,EAAM;YAAc;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1B,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,eAENP,OAAA;UAAKE,SAAS,EAAC,WAAW;UAAAC,QAAA,gBACtBH,OAAA;YAAGE,SAAS,EAAC,YAAY;YAAAC,QAAA,EAAC;UAAkC;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eAChEP,OAAA;YAAKE,SAAS,EAAC,cAAc;YAAAC,QAAA,gBACzBH,OAAA;cAAME,SAAS,EAAC,aAAa;cAAAC,QAAA,EAAC;YAAsB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAC3DP,OAAA;cAAME,SAAS,EAAC,aAAa;cAAAC,QAAA,EAAC;YAAc;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACnDP,OAAA;cAAME,SAAS,EAAC,aAAa;cAAAC,QAAA,EAAC;YAAgB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACD,CAAC;AAElB;AAACE,EAAA,GA1DQR,GAAG;AA4DZ,eAAeA,GAAG;AAAC,IAAAQ,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}