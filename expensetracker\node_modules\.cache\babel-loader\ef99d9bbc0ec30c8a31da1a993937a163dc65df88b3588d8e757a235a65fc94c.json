{"ast": null, "code": "var _jsxFileName = \"D:\\\\Expense Tracker App (MERN )\\\\expensetracker\\\\src\\\\App.js\",\n  _s = $RefreshSig$();\nimport { BrowserRouter as Router, Routes, Route, useLocation } from \"react-router-dom\";\nimport { useEffect, useState } from \"react\";\nimport Navbar from \"./components/Navbar\";\nimport Loader from \"./components/Loader\";\nimport Home from \"./pages/Home\";\nimport About from \"./pages/About\";\nimport Contact from \"./pages/Contact\";\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nfunction AppContent() {\n  _s();\n  const location = useLocation();\n  const [loading, setLoading] = useState(false);\n  useEffect(() => {\n    setLoading(true);\n    const timer = setTimeout(() => {\n      setLoading(false);\n    }, 500); // half a second delay\n\n    return () => clearTimeout(timer);\n  }, [location]);\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(Loader, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 24,\n      columnNumber: 12\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: /*#__PURE__*/_jsxDEV(Routes, {\n      children: [/*#__PURE__*/_jsxDEV(Route, {\n        path: \"/\",\n        element: /*#__PURE__*/_jsxDEV(Home, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 30,\n          columnNumber: 34\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 30,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Route, {\n        path: \"/about\",\n        element: /*#__PURE__*/_jsxDEV(About, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 31,\n          columnNumber: 39\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 31,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Route, {\n        path: \"/contact\",\n        element: /*#__PURE__*/_jsxDEV(Contact, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 32,\n          columnNumber: 41\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 32,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 29,\n      columnNumber: 7\n    }, this)\n  }, void 0, false);\n}\n_s(AppContent, \"12V4DhK/+FphMBmcfsoKCCzma6g=\", false, function () {\n  return [useLocation];\n});\n_c = AppContent;\nfunction App() {\n  return /*#__PURE__*/_jsxDEV(Router, {\n    children: /*#__PURE__*/_jsxDEV(AppContent, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 41,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 40,\n    columnNumber: 5\n  }, this);\n}\n_c2 = App;\nexport default App;\nvar _c, _c2;\n$RefreshReg$(_c, \"AppContent\");\n$RefreshReg$(_c2, \"App\");", "map": {"version": 3, "names": ["<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Router", "Routes", "Route", "useLocation", "useEffect", "useState", "<PERSON><PERSON><PERSON>", "Loader", "Home", "About", "Contact", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "A<PERSON><PERSON><PERSON>nt", "_s", "location", "loading", "setLoading", "timer", "setTimeout", "clearTimeout", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "children", "path", "element", "_c", "App", "_c2", "$RefreshReg$"], "sources": ["D:/Expense Tracker App (MERN )/expensetracker/src/App.js"], "sourcesContent": ["import { BrowserRouter as Router, Routes, Route, useLocation } from \"react-router-dom\";\nimport { useEffect, useState } from \"react\";\nimport Navbar from \"./components/Navbar\";\nimport Loader from \"./components/Loader\";\nimport Home from \"./pages/Home\";\nimport About from \"./pages/About\";\nimport Contact  from \"./pages/Contact\";\n\nfunction AppContent() {\n  const location = useLocation();\n  const [loading, setLoading] = useState(false);\n\n  useEffect(() => {\n    setLoading(true);\n\n    const timer = setTimeout(() => {\n      setLoading(false);\n    }, 500); // half a second delay\n\n    return () => clearTimeout(timer);\n  }, [location]);\n\n  if (loading) {\n    return <Loader />;\n  }\n  return (\n    <>\n      \n      <Routes>\n        <Route path=\"/\" element={<Home />} />\n        <Route path=\"/about\" element={<About />} />\n        <Route path=\"/contact\" element={<Contact />} />\n      </Routes>\n    </>\n  );\n}\n\nfunction App() {\n  return (\n    <Router>\n      <AppContent />\n    </Router>\n  );\n}\n\nexport default App;\n"], "mappings": ";;AAAA,SAASA,aAAa,IAAIC,MAAM,EAAEC,MAAM,EAAEC,KAAK,EAAEC,WAAW,QAAQ,kBAAkB;AACtF,SAASC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAC3C,OAAOC,MAAM,MAAM,qBAAqB;AACxC,OAAOC,MAAM,MAAM,qBAAqB;AACxC,OAAOC,IAAI,MAAM,cAAc;AAC/B,OAAOC,KAAK,MAAM,eAAe;AACjC,OAAOC,OAAO,MAAO,iBAAiB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAEvC,SAASC,UAAUA,CAAA,EAAG;EAAAC,EAAA;EACpB,MAAMC,QAAQ,GAAGd,WAAW,CAAC,CAAC;EAC9B,MAAM,CAACe,OAAO,EAAEC,UAAU,CAAC,GAAGd,QAAQ,CAAC,KAAK,CAAC;EAE7CD,SAAS,CAAC,MAAM;IACde,UAAU,CAAC,IAAI,CAAC;IAEhB,MAAMC,KAAK,GAAGC,UAAU,CAAC,MAAM;MAC7BF,UAAU,CAAC,KAAK,CAAC;IACnB,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC;;IAET,OAAO,MAAMG,YAAY,CAACF,KAAK,CAAC;EAClC,CAAC,EAAE,CAACH,QAAQ,CAAC,CAAC;EAEd,IAAIC,OAAO,EAAE;IACX,oBAAON,OAAA,CAACL,MAAM;MAAAgB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EACnB;EACA,oBACEd,OAAA,CAAAE,SAAA;IAAAa,QAAA,eAEEf,OAAA,CAACX,MAAM;MAAA0B,QAAA,gBACLf,OAAA,CAACV,KAAK;QAAC0B,IAAI,EAAC,GAAG;QAACC,OAAO,eAAEjB,OAAA,CAACJ,IAAI;UAAAe,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAE;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACrCd,OAAA,CAACV,KAAK;QAAC0B,IAAI,EAAC,QAAQ;QAACC,OAAO,eAAEjB,OAAA,CAACH,KAAK;UAAAc,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAE;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAC3Cd,OAAA,CAACV,KAAK;QAAC0B,IAAI,EAAC,UAAU;QAACC,OAAO,eAAEjB,OAAA,CAACF,OAAO;UAAAa,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAE;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACzC;EAAC,gBACT,CAAC;AAEP;AAACV,EAAA,CA3BQD,UAAU;EAAA,QACAZ,WAAW;AAAA;AAAA2B,EAAA,GADrBf,UAAU;AA6BnB,SAASgB,GAAGA,CAAA,EAAG;EACb,oBACEnB,OAAA,CAACZ,MAAM;IAAA2B,QAAA,eACLf,OAAA,CAACG,UAAU;MAAAQ,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACR,CAAC;AAEb;AAACM,GAAA,GANQD,GAAG;AAQZ,eAAeA,GAAG;AAAC,IAAAD,EAAA,EAAAE,GAAA;AAAAC,YAAA,CAAAH,EAAA;AAAAG,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}