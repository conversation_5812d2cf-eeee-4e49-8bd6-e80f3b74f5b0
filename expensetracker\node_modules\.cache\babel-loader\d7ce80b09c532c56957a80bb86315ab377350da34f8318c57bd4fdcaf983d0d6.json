{"ast": null, "code": "var _jsxFileName = \"D:\\\\Expense Tracker App (MERN )\\\\expensetracker\\\\src\\\\components\\\\Loader.js\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction Loader() {\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"d-flex justify-content-center align-items-center\",\n    style: {\n      height: \"100vh\"\n    },\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"spinner-border text-primary\",\n      role: \"status\",\n      children: /*#__PURE__*/_jsxDEV(\"span\", {\n        className: \"visually-hidden\",\n        children: \"Loading...\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 5,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 4,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 3,\n    columnNumber: 5\n  }, this);\n}\n_c = Loader;\nexport default Loader;\nvar _c;\n$RefreshReg$(_c, \"Loader\");", "map": {"version": 3, "names": ["Loader", "_jsxDEV", "className", "style", "height", "children", "role", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["D:/Expense Tracker App (MERN )/expensetracker/src/components/Loader.js"], "sourcesContent": ["function Loader() {\r\n  return (\r\n    <div className=\"d-flex justify-content-center align-items-center\" style={{ height: \"100vh\" }}>\r\n      <div className=\"spinner-border text-primary\" role=\"status\">\r\n        <span className=\"visually-hidden\" >Loading...</span>\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n\r\nexport default Loader;\r\n"], "mappings": ";;AAAA,SAASA,MAAMA,CAAA,EAAG;EAChB,oBACEC,OAAA;IAAKC,SAAS,EAAC,kDAAkD;IAACC,KAAK,EAAE;MAAEC,MAAM,EAAE;IAAQ,CAAE;IAAAC,QAAA,eAC3FJ,OAAA;MAAKC,SAAS,EAAC,6BAA6B;MAACI,IAAI,EAAC,QAAQ;MAAAD,QAAA,eACxDJ,OAAA;QAAMC,SAAS,EAAC,iBAAiB;QAAAG,QAAA,EAAE;MAAU;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACjD;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV;AAACC,EAAA,GARQX,MAAM;AAUf,eAAeA,MAAM;AAAC,IAAAW,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}