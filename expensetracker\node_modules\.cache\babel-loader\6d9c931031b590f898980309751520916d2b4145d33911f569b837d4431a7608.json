{"ast": null, "code": "var _jsxFileName = \"D:\\\\Expense Tracker App (MERN )\\\\expensetracker\\\\src\\\\pages\\\\Contact.js\",\n  _s = $RefreshSig$();\nimport { useState } from 'react';\nimport { Mail, Phone, MapPin, Clock, Send, CheckCircle } from 'lucide-react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction Contact() {\n  _s();\n  const [formData, setFormData] = useState({\n    name: '',\n    email: '',\n    subject: '',\n    message: ''\n  });\n  const [isSubmitted, setIsSubmitted] = useState(false);\n  const handleChange = e => {\n    setFormData({\n      ...formData,\n      [e.target.name]: e.target.value\n    });\n  };\n  const handleSubmit = () => {\n    // Simulate form submission\n    setIsSubmitted(true);\n    setTimeout(() => {\n      setIsSubmitted(false);\n      setFormData({\n        name: '',\n        email: '',\n        subject: '',\n        message: ''\n      });\n    }, 3000);\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 py-12 px-4 sm:px-6 lg:px-8\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"max-w-7xl mx-auto\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center mb-16\",\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          className: \"text-4xl md:text-5xl font-bold text-gray-900 mb-4\",\n          children: \"Get In Touch\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 34,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-xl text-gray-600 max-w-2xl mx-auto\",\n          children: \"We'd love to hear from you. Send us a message and we'll respond as soon as possible.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 37,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 33,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid grid-cols-1 lg:grid-cols-3 gap-8\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"lg:col-span-1\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-white rounded-2xl shadow-xl p-8 h-fit\",\n            children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n              className: \"text-2xl font-bold text-gray-900 mb-6\",\n              children: \"Contact Information\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 46,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"space-y-6\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-start space-x-4\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex-shrink-0\",\n                  children: /*#__PURE__*/_jsxDEV(Mail, {\n                    className: \"w-6 h-6 text-blue-600\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 51,\n                    columnNumber: 41\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 50,\n                  columnNumber: 37\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                    className: \"text-lg font-medium text-gray-900\",\n                    children: \"Email\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 54,\n                    columnNumber: 41\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-gray-600\",\n                    children: \"<EMAIL>\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 55,\n                    columnNumber: 41\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-gray-600\",\n                    children: \"<EMAIL>\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 56,\n                    columnNumber: 41\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 53,\n                  columnNumber: 37\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 49,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-start space-x-4\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex-shrink-0\",\n                  children: /*#__PURE__*/_jsxDEV(Phone, {\n                    className: \"w-6 h-6 text-blue-600\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 62,\n                    columnNumber: 41\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 61,\n                  columnNumber: 37\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                    className: \"text-lg font-medium text-gray-900\",\n                    children: \"Phone\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 65,\n                    columnNumber: 41\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-gray-600\",\n                    children: \"+****************\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 66,\n                    columnNumber: 41\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-gray-600\",\n                    children: \"+****************\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 67,\n                    columnNumber: 41\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 64,\n                  columnNumber: 37\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 60,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-start space-x-4\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex-shrink-0\",\n                  children: /*#__PURE__*/_jsxDEV(MapPin, {\n                    className: \"w-6 h-6 text-blue-600\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 73,\n                    columnNumber: 41\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 72,\n                  columnNumber: 37\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                    className: \"text-lg font-medium text-gray-900\",\n                    children: \"Address\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 76,\n                    columnNumber: 41\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-gray-600\",\n                    children: [\"123 Business Street\", /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 78,\n                      columnNumber: 64\n                    }, this), \"Suite 100\", /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 79,\n                      columnNumber: 54\n                    }, this), \"New York, NY 10001\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 77,\n                    columnNumber: 41\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 75,\n                  columnNumber: 37\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 71,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-start space-x-4\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex-shrink-0\",\n                  children: /*#__PURE__*/_jsxDEV(Clock, {\n                    className: \"w-6 h-6 text-blue-600\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 87,\n                    columnNumber: 41\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 86,\n                  columnNumber: 37\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                    className: \"text-lg font-medium text-gray-900\",\n                    children: \"Business Hours\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 90,\n                    columnNumber: 41\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-gray-600\",\n                    children: [\"Monday - Friday: 9:00 AM - 6:00 PM\", /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 92,\n                      columnNumber: 79\n                    }, this), \"Saturday: 10:00 AM - 4:00 PM\", /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 93,\n                      columnNumber: 73\n                    }, this), \"Sunday: Closed\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 91,\n                    columnNumber: 41\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 89,\n                  columnNumber: 37\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 85,\n                columnNumber: 33\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 48,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mt-8 pt-8 border-t border-gray-200\",\n              children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                className: \"text-lg font-medium text-gray-900 mb-4\",\n                children: \"Follow Us\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 102,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex space-x-4\",\n                children: [/*#__PURE__*/_jsxDEV(\"a\", {\n                  href: \"#\",\n                  className: \"text-gray-400 hover:text-blue-600 transition-colors\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"sr-only\",\n                    children: \"Facebook\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 105,\n                    columnNumber: 41\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center text-white text-sm font-bold\",\n                    children: \"f\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 106,\n                    columnNumber: 41\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 104,\n                  columnNumber: 37\n                }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n                  href: \"#\",\n                  className: \"text-gray-400 hover:text-blue-400 transition-colors\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"sr-only\",\n                    children: \"Twitter\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 109,\n                    columnNumber: 41\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"w-8 h-8 bg-blue-400 rounded-full flex items-center justify-center text-white text-sm font-bold\",\n                    children: \"t\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 110,\n                    columnNumber: 41\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 108,\n                  columnNumber: 37\n                }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n                  href: \"#\",\n                  className: \"text-gray-400 hover:text-blue-700 transition-colors\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"sr-only\",\n                    children: \"LinkedIn\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 113,\n                    columnNumber: 41\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"w-8 h-8 bg-blue-700 rounded-full flex items-center justify-center text-white text-sm font-bold\",\n                    children: \"in\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 114,\n                    columnNumber: 41\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 112,\n                  columnNumber: 37\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 103,\n                columnNumber: 33\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 101,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 45,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 44,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"lg:col-span-2\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-white rounded-2xl shadow-xl p-8\",\n            children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n              className: \"text-2xl font-bold text-gray-900 mb-6\",\n              children: \"Send us a Message\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 124,\n              columnNumber: 29\n            }, this), isSubmitted ? /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-center py-12\",\n              children: [/*#__PURE__*/_jsxDEV(CheckCircle, {\n                className: \"w-16 h-16 text-green-500 mx-auto mb-4\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 128,\n                columnNumber: 37\n              }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                className: \"text-2xl font-bold text-gray-900 mb-2\",\n                children: \"Message Sent!\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 129,\n                columnNumber: 37\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-gray-600\",\n                children: \"Thank you for your message. We'll get back to you soon.\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 130,\n                columnNumber: 37\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 127,\n              columnNumber: 33\n            }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"space-y-6\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"grid grid-cols-1 sm:grid-cols-2 gap-6\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    htmlFor: \"name\",\n                    className: \"block text-sm font-medium text-gray-700 mb-2\",\n                    children: \"Full Name *\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 136,\n                    columnNumber: 45\n                  }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"text\",\n                    id: \"name\",\n                    name: \"name\",\n                    value: formData.name,\n                    onChange: handleChange,\n                    required: true,\n                    className: \"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-colors\",\n                    placeholder: \"John Doe\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 139,\n                    columnNumber: 45\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 135,\n                  columnNumber: 41\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    htmlFor: \"email\",\n                    className: \"block text-sm font-medium text-gray-700 mb-2\",\n                    children: \"Email Address *\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 151,\n                    columnNumber: 45\n                  }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"email\",\n                    id: \"email\",\n                    name: \"email\",\n                    value: formData.email,\n                    onChange: handleChange,\n                    required: true,\n                    className: \"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-colors\",\n                    placeholder: \"<EMAIL>\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 154,\n                    columnNumber: 45\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 150,\n                  columnNumber: 41\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 134,\n                columnNumber: 37\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  htmlFor: \"subject\",\n                  className: \"block text-sm font-medium text-gray-700 mb-2\",\n                  children: \"Subject *\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 168,\n                  columnNumber: 41\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"text\",\n                  id: \"subject\",\n                  name: \"subject\",\n                  value: formData.subject,\n                  onChange: handleChange,\n                  required: true,\n                  className: \"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-colors\",\n                  placeholder: \"How can we help you?\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 171,\n                  columnNumber: 41\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 167,\n                columnNumber: 37\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  htmlFor: \"message\",\n                  className: \"block text-sm font-medium text-gray-700 mb-2\",\n                  children: \"Message *\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 184,\n                  columnNumber: 41\n                }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n                  id: \"message\",\n                  name: \"message\",\n                  rows: 6,\n                  value: formData.message,\n                  onChange: handleChange,\n                  required: true,\n                  className: \"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-colors resize-none\",\n                  placeholder: \"Tell us more about your inquiry...\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 187,\n                  columnNumber: 41\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 183,\n                columnNumber: 37\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center\",\n                children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                  id: \"newsletter\",\n                  type: \"checkbox\",\n                  className: \"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 200,\n                  columnNumber: 41\n                }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n                  htmlFor: \"newsletter\",\n                  className: \"ml-2 block text-sm text-gray-700\",\n                  children: \"Subscribe to our newsletter for updates and news\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 205,\n                  columnNumber: 41\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 199,\n                columnNumber: 37\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                type: \"button\",\n                onClick: handleSubmit,\n                className: \"w-full bg-blue-600 text-white py-3 px-6 rounded-lg hover:bg-blue-700 focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-colors flex items-center justify-center space-x-2 font-medium\",\n                children: [/*#__PURE__*/_jsxDEV(Send, {\n                  className: \"w-5 h-5\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 215,\n                  columnNumber: 41\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  children: \"Send Message\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 216,\n                  columnNumber: 41\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 210,\n                columnNumber: 37\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 133,\n              columnNumber: 33\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 123,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 122,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 42,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mt-16\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-white rounded-2xl shadow-xl p-8\",\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            className: \"text-2xl font-bold text-gray-900 mb-6 text-center\",\n            children: \"Find Us\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 227,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-gray-200 rounded-lg h-64 flex items-center justify-center\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-center\",\n              children: [/*#__PURE__*/_jsxDEV(MapPin, {\n                className: \"w-12 h-12 text-gray-400 mx-auto mb-2\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 230,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-gray-600\",\n                children: \"Interactive map would be embedded here\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 231,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-sm text-gray-500\",\n                children: \"123 Business Street, New York, NY 10001\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 232,\n                columnNumber: 33\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 229,\n              columnNumber: 29\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 228,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 226,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 225,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 31,\n      columnNumber: 13\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 30,\n    columnNumber: 9\n  }, this);\n}\n_s(Contact, \"weclwNi2IITJtGYODtTTcZ3otZw=\");\n_c = Contact;\nexport default Contact;\nvar _c;\n$RefreshReg$(_c, \"Contact\");", "map": {"version": 3, "names": ["useState", "Mail", "Phone", "MapPin", "Clock", "Send", "CheckCircle", "jsxDEV", "_jsxDEV", "Contact", "_s", "formData", "setFormData", "name", "email", "subject", "message", "isSubmitted", "setIsSubmitted", "handleChange", "e", "target", "value", "handleSubmit", "setTimeout", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "href", "htmlFor", "type", "id", "onChange", "required", "placeholder", "rows", "onClick", "_c", "$RefreshReg$"], "sources": ["D:/Expense Tracker App (MERN )/expensetracker/src/pages/Contact.js"], "sourcesContent": ["import { useState } from 'react';\r\nimport { Mail, Phone, MapPin, Clock, Send, CheckCircle } from 'lucide-react';\r\n\r\nfunction Contact() {\r\n    const [formData, setFormData] = useState({\r\n        name: '',\r\n        email: '',\r\n        subject: '',\r\n        message: ''\r\n    });\r\n    const [isSubmitted, setIsSubmitted] = useState(false);\r\n\r\n    const handleChange = (e) => {\r\n        setFormData({\r\n            ...formData,\r\n            [e.target.name]: e.target.value\r\n        });\r\n    };\r\n\r\n    const handleSubmit = () => {\r\n        // Simulate form submission\r\n        setIsSubmitted(true);\r\n        setTimeout(() => {\r\n            setIsSubmitted(false);\r\n            setFormData({ name: '', email: '', subject: '', message: '' });\r\n        }, 3000);\r\n    };\r\n\r\n    return (\r\n        <div className=\"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 py-12 px-4 sm:px-6 lg:px-8\">\r\n            <div className=\"max-w-7xl mx-auto\">\r\n                {/* Header */}\r\n                <div className=\"text-center mb-16\">\r\n                    <h1 className=\"text-4xl md:text-5xl font-bold text-gray-900 mb-4\">\r\n                        Get In Touch\r\n                    </h1>\r\n                    <p className=\"text-xl text-gray-600 max-w-2xl mx-auto\">\r\n                        We'd love to hear from you. Send us a message and we'll respond as soon as possible.\r\n                    </p>\r\n                </div>\r\n\r\n                <div className=\"grid grid-cols-1 lg:grid-cols-3 gap-8\">\r\n                    {/* Contact Information */}\r\n                    <div className=\"lg:col-span-1\">\r\n                        <div className=\"bg-white rounded-2xl shadow-xl p-8 h-fit\">\r\n                            <h2 className=\"text-2xl font-bold text-gray-900 mb-6\">Contact Information</h2>\r\n\r\n                            <div className=\"space-y-6\">\r\n                                <div className=\"flex items-start space-x-4\">\r\n                                    <div className=\"flex-shrink-0\">\r\n                                        <Mail className=\"w-6 h-6 text-blue-600\" />\r\n                                    </div>\r\n                                    <div>\r\n                                        <h3 className=\"text-lg font-medium text-gray-900\">Email</h3>\r\n                                        <p className=\"text-gray-600\"><EMAIL></p>\r\n                                        <p className=\"text-gray-600\"><EMAIL></p>\r\n                                    </div>\r\n                                </div>\r\n\r\n                                <div className=\"flex items-start space-x-4\">\r\n                                    <div className=\"flex-shrink-0\">\r\n                                        <Phone className=\"w-6 h-6 text-blue-600\" />\r\n                                    </div>\r\n                                    <div>\r\n                                        <h3 className=\"text-lg font-medium text-gray-900\">Phone</h3>\r\n                                        <p className=\"text-gray-600\">+****************</p>\r\n                                        <p className=\"text-gray-600\">+****************</p>\r\n                                    </div>\r\n                                </div>\r\n\r\n                                <div className=\"flex items-start space-x-4\">\r\n                                    <div className=\"flex-shrink-0\">\r\n                                        <MapPin className=\"w-6 h-6 text-blue-600\" />\r\n                                    </div>\r\n                                    <div>\r\n                                        <h3 className=\"text-lg font-medium text-gray-900\">Address</h3>\r\n                                        <p className=\"text-gray-600\">\r\n                                            123 Business Street<br />\r\n                                            Suite 100<br />\r\n                                            New York, NY 10001\r\n                                        </p>\r\n                                    </div>\r\n                                </div>\r\n\r\n                                <div className=\"flex items-start space-x-4\">\r\n                                    <div className=\"flex-shrink-0\">\r\n                                        <Clock className=\"w-6 h-6 text-blue-600\" />\r\n                                    </div>\r\n                                    <div>\r\n                                        <h3 className=\"text-lg font-medium text-gray-900\">Business Hours</h3>\r\n                                        <p className=\"text-gray-600\">\r\n                                            Monday - Friday: 9:00 AM - 6:00 PM<br />\r\n                                            Saturday: 10:00 AM - 4:00 PM<br />\r\n                                            Sunday: Closed\r\n                                        </p>\r\n                                    </div>\r\n                                </div>\r\n                            </div>\r\n\r\n                            {/* Social Media Links */}\r\n                            <div className=\"mt-8 pt-8 border-t border-gray-200\">\r\n                                <h3 className=\"text-lg font-medium text-gray-900 mb-4\">Follow Us</h3>\r\n                                <div className=\"flex space-x-4\">\r\n                                    <a href=\"#\" className=\"text-gray-400 hover:text-blue-600 transition-colors\">\r\n                                        <span className=\"sr-only\">Facebook</span>\r\n                                        <div className=\"w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center text-white text-sm font-bold\">f</div>\r\n                                    </a>\r\n                                    <a href=\"#\" className=\"text-gray-400 hover:text-blue-400 transition-colors\">\r\n                                        <span className=\"sr-only\">Twitter</span>\r\n                                        <div className=\"w-8 h-8 bg-blue-400 rounded-full flex items-center justify-center text-white text-sm font-bold\">t</div>\r\n                                    </a>\r\n                                    <a href=\"#\" className=\"text-gray-400 hover:text-blue-700 transition-colors\">\r\n                                        <span className=\"sr-only\">LinkedIn</span>\r\n                                        <div className=\"w-8 h-8 bg-blue-700 rounded-full flex items-center justify-center text-white text-sm font-bold\">in</div>\r\n                                    </a>\r\n                                </div>\r\n                            </div>\r\n                        </div>\r\n                    </div>\r\n\r\n                    {/* Contact Form */}\r\n                    <div className=\"lg:col-span-2\">\r\n                        <div className=\"bg-white rounded-2xl shadow-xl p-8\">\r\n                            <h2 className=\"text-2xl font-bold text-gray-900 mb-6\">Send us a Message</h2>\r\n\r\n                            {isSubmitted ? (\r\n                                <div className=\"text-center py-12\">\r\n                                    <CheckCircle className=\"w-16 h-16 text-green-500 mx-auto mb-4\" />\r\n                                    <h3 className=\"text-2xl font-bold text-gray-900 mb-2\">Message Sent!</h3>\r\n                                    <p className=\"text-gray-600\">Thank you for your message. We'll get back to you soon.</p>\r\n                                </div>\r\n                            ) : (\r\n                                <div className=\"space-y-6\">\r\n                                    <div className=\"grid grid-cols-1 sm:grid-cols-2 gap-6\">\r\n                                        <div>\r\n                                            <label htmlFor=\"name\" className=\"block text-sm font-medium text-gray-700 mb-2\">\r\n                                                Full Name *\r\n                                            </label>\r\n                                            <input\r\n                                                type=\"text\"\r\n                                                id=\"name\"\r\n                                                name=\"name\"\r\n                                                value={formData.name}\r\n                                                onChange={handleChange}\r\n                                                required\r\n                                                className=\"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-colors\"\r\n                                                placeholder=\"John Doe\"\r\n                                            />\r\n                                        </div>\r\n                                        <div>\r\n                                            <label htmlFor=\"email\" className=\"block text-sm font-medium text-gray-700 mb-2\">\r\n                                                Email Address *\r\n                                            </label>\r\n                                            <input\r\n                                                type=\"email\"\r\n                                                id=\"email\"\r\n                                                name=\"email\"\r\n                                                value={formData.email}\r\n                                                onChange={handleChange}\r\n                                                required\r\n                                                className=\"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-colors\"\r\n                                                placeholder=\"<EMAIL>\"\r\n                                            />\r\n                                        </div>\r\n                                    </div>\r\n\r\n                                    <div>\r\n                                        <label htmlFor=\"subject\" className=\"block text-sm font-medium text-gray-700 mb-2\">\r\n                                            Subject *\r\n                                        </label>\r\n                                        <input\r\n                                            type=\"text\"\r\n                                            id=\"subject\"\r\n                                            name=\"subject\"\r\n                                            value={formData.subject}\r\n                                            onChange={handleChange}\r\n                                            required\r\n                                            className=\"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-colors\"\r\n                                            placeholder=\"How can we help you?\"\r\n                                        />\r\n                                    </div>\r\n\r\n                                    <div>\r\n                                        <label htmlFor=\"message\" className=\"block text-sm font-medium text-gray-700 mb-2\">\r\n                                            Message *\r\n                                        </label>\r\n                                        <textarea\r\n                                            id=\"message\"\r\n                                            name=\"message\"\r\n                                            rows={6}\r\n                                            value={formData.message}\r\n                                            onChange={handleChange}\r\n                                            required\r\n                                            className=\"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-colors resize-none\"\r\n                                            placeholder=\"Tell us more about your inquiry...\"\r\n                                        ></textarea>\r\n                                    </div>\r\n\r\n                                    <div className=\"flex items-center\">\r\n                                        <input\r\n                                            id=\"newsletter\"\r\n                                            type=\"checkbox\"\r\n                                            className=\"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded\"\r\n                                        />\r\n                                        <label htmlFor=\"newsletter\" className=\"ml-2 block text-sm text-gray-700\">\r\n                                            Subscribe to our newsletter for updates and news\r\n                                        </label>\r\n                                    </div>\r\n\r\n                                    <button\r\n                                        type=\"button\"\r\n                                        onClick={handleSubmit}\r\n                                        className=\"w-full bg-blue-600 text-white py-3 px-6 rounded-lg hover:bg-blue-700 focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-colors flex items-center justify-center space-x-2 font-medium\"\r\n                                    >\r\n                                        <Send className=\"w-5 h-5\" />\r\n                                        <span>Send Message</span>\r\n                                    </button>\r\n                                </div>\r\n                            )}\r\n                        </div>\r\n                    </div>\r\n                </div>\r\n\r\n                {/* Map Section */}\r\n                <div className=\"mt-16\">\r\n                    <div className=\"bg-white rounded-2xl shadow-xl p-8\">\r\n                        <h2 className=\"text-2xl font-bold text-gray-900 mb-6 text-center\">Find Us</h2>\r\n                        <div className=\"bg-gray-200 rounded-lg h-64 flex items-center justify-center\">\r\n                            <div className=\"text-center\">\r\n                                <MapPin className=\"w-12 h-12 text-gray-400 mx-auto mb-2\" />\r\n                                <p className=\"text-gray-600\">Interactive map would be embedded here</p>\r\n                                <p className=\"text-sm text-gray-500\">123 Business Street, New York, NY 10001</p>\r\n                            </div>\r\n                        </div>\r\n                    </div>\r\n                </div>\r\n            </div>\r\n        </div>\r\n    );\r\n}\r\n\r\nexport default Contact;"], "mappings": ";;AAAA,SAASA,QAAQ,QAAQ,OAAO;AAChC,SAASC,IAAI,EAAEC,KAAK,EAAEC,MAAM,EAAEC,KAAK,EAAEC,IAAI,EAAEC,WAAW,QAAQ,cAAc;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE7E,SAASC,OAAOA,CAAA,EAAG;EAAAC,EAAA;EACf,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGZ,QAAQ,CAAC;IACrCa,IAAI,EAAE,EAAE;IACRC,KAAK,EAAE,EAAE;IACTC,OAAO,EAAE,EAAE;IACXC,OAAO,EAAE;EACb,CAAC,CAAC;EACF,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGlB,QAAQ,CAAC,KAAK,CAAC;EAErD,MAAMmB,YAAY,GAAIC,CAAC,IAAK;IACxBR,WAAW,CAAC;MACR,GAAGD,QAAQ;MACX,CAACS,CAAC,CAACC,MAAM,CAACR,IAAI,GAAGO,CAAC,CAACC,MAAM,CAACC;IAC9B,CAAC,CAAC;EACN,CAAC;EAED,MAAMC,YAAY,GAAGA,CAAA,KAAM;IACvB;IACAL,cAAc,CAAC,IAAI,CAAC;IACpBM,UAAU,CAAC,MAAM;MACbN,cAAc,CAAC,KAAK,CAAC;MACrBN,WAAW,CAAC;QAAEC,IAAI,EAAE,EAAE;QAAEC,KAAK,EAAE,EAAE;QAAEC,OAAO,EAAE,EAAE;QAAEC,OAAO,EAAE;MAAG,CAAC,CAAC;IAClE,CAAC,EAAE,IAAI,CAAC;EACZ,CAAC;EAED,oBACIR,OAAA;IAAKiB,SAAS,EAAC,sFAAsF;IAAAC,QAAA,eACjGlB,OAAA;MAAKiB,SAAS,EAAC,mBAAmB;MAAAC,QAAA,gBAE9BlB,OAAA;QAAKiB,SAAS,EAAC,mBAAmB;QAAAC,QAAA,gBAC9BlB,OAAA;UAAIiB,SAAS,EAAC,mDAAmD;UAAAC,QAAA,EAAC;QAElE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACLtB,OAAA;UAAGiB,SAAS,EAAC,yCAAyC;UAAAC,QAAA,EAAC;QAEvD;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENtB,OAAA;QAAKiB,SAAS,EAAC,uCAAuC;QAAAC,QAAA,gBAElDlB,OAAA;UAAKiB,SAAS,EAAC,eAAe;UAAAC,QAAA,eAC1BlB,OAAA;YAAKiB,SAAS,EAAC,0CAA0C;YAAAC,QAAA,gBACrDlB,OAAA;cAAIiB,SAAS,EAAC,uCAAuC;cAAAC,QAAA,EAAC;YAAmB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAE9EtB,OAAA;cAAKiB,SAAS,EAAC,WAAW;cAAAC,QAAA,gBACtBlB,OAAA;gBAAKiB,SAAS,EAAC,4BAA4B;gBAAAC,QAAA,gBACvClB,OAAA;kBAAKiB,SAAS,EAAC,eAAe;kBAAAC,QAAA,eAC1BlB,OAAA,CAACP,IAAI;oBAACwB,SAAS,EAAC;kBAAuB;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACzC,CAAC,eACNtB,OAAA;kBAAAkB,QAAA,gBACIlB,OAAA;oBAAIiB,SAAS,EAAC,mCAAmC;oBAAAC,QAAA,EAAC;kBAAK;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAC5DtB,OAAA;oBAAGiB,SAAS,EAAC,eAAe;oBAAAC,QAAA,EAAC;kBAAiB;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CAAC,eAClDtB,OAAA;oBAAGiB,SAAS,EAAC,eAAe;oBAAAC,QAAA,EAAC;kBAAmB;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnD,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC,eAENtB,OAAA;gBAAKiB,SAAS,EAAC,4BAA4B;gBAAAC,QAAA,gBACvClB,OAAA;kBAAKiB,SAAS,EAAC,eAAe;kBAAAC,QAAA,eAC1BlB,OAAA,CAACN,KAAK;oBAACuB,SAAS,EAAC;kBAAuB;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1C,CAAC,eACNtB,OAAA;kBAAAkB,QAAA,gBACIlB,OAAA;oBAAIiB,SAAS,EAAC,mCAAmC;oBAAAC,QAAA,EAAC;kBAAK;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAC5DtB,OAAA;oBAAGiB,SAAS,EAAC,eAAe;oBAAAC,QAAA,EAAC;kBAAiB;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CAAC,eAClDtB,OAAA;oBAAGiB,SAAS,EAAC,eAAe;oBAAAC,QAAA,EAAC;kBAAiB;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACjD,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC,eAENtB,OAAA;gBAAKiB,SAAS,EAAC,4BAA4B;gBAAAC,QAAA,gBACvClB,OAAA;kBAAKiB,SAAS,EAAC,eAAe;kBAAAC,QAAA,eAC1BlB,OAAA,CAACL,MAAM;oBAACsB,SAAS,EAAC;kBAAuB;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC3C,CAAC,eACNtB,OAAA;kBAAAkB,QAAA,gBACIlB,OAAA;oBAAIiB,SAAS,EAAC,mCAAmC;oBAAAC,QAAA,EAAC;kBAAO;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAC9DtB,OAAA;oBAAGiB,SAAS,EAAC,eAAe;oBAAAC,QAAA,GAAC,qBACN,eAAAlB,OAAA;sBAAAmB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,aAChB,eAAAtB,OAAA;sBAAAmB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,sBAEnB;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC,eAENtB,OAAA;gBAAKiB,SAAS,EAAC,4BAA4B;gBAAAC,QAAA,gBACvClB,OAAA;kBAAKiB,SAAS,EAAC,eAAe;kBAAAC,QAAA,eAC1BlB,OAAA,CAACJ,KAAK;oBAACqB,SAAS,EAAC;kBAAuB;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1C,CAAC,eACNtB,OAAA;kBAAAkB,QAAA,gBACIlB,OAAA;oBAAIiB,SAAS,EAAC,mCAAmC;oBAAAC,QAAA,EAAC;kBAAc;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACrEtB,OAAA;oBAAGiB,SAAS,EAAC,eAAe;oBAAAC,QAAA,GAAC,oCACS,eAAAlB,OAAA;sBAAAmB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,gCACZ,eAAAtB,OAAA;sBAAAmB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,kBAEtC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC,eAGNtB,OAAA;cAAKiB,SAAS,EAAC,oCAAoC;cAAAC,QAAA,gBAC/ClB,OAAA;gBAAIiB,SAAS,EAAC,wCAAwC;gBAAAC,QAAA,EAAC;cAAS;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACrEtB,OAAA;gBAAKiB,SAAS,EAAC,gBAAgB;gBAAAC,QAAA,gBAC3BlB,OAAA;kBAAGuB,IAAI,EAAC,GAAG;kBAACN,SAAS,EAAC,qDAAqD;kBAAAC,QAAA,gBACvElB,OAAA;oBAAMiB,SAAS,EAAC,SAAS;oBAAAC,QAAA,EAAC;kBAAQ;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eACzCtB,OAAA;oBAAKiB,SAAS,EAAC,gGAAgG;oBAAAC,QAAA,EAAC;kBAAC;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACxH,CAAC,eACJtB,OAAA;kBAAGuB,IAAI,EAAC,GAAG;kBAACN,SAAS,EAAC,qDAAqD;kBAAAC,QAAA,gBACvElB,OAAA;oBAAMiB,SAAS,EAAC,SAAS;oBAAAC,QAAA,EAAC;kBAAO;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eACxCtB,OAAA;oBAAKiB,SAAS,EAAC,gGAAgG;oBAAAC,QAAA,EAAC;kBAAC;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACxH,CAAC,eACJtB,OAAA;kBAAGuB,IAAI,EAAC,GAAG;kBAACN,SAAS,EAAC,qDAAqD;kBAAAC,QAAA,gBACvElB,OAAA;oBAAMiB,SAAS,EAAC,SAAS;oBAAAC,QAAA,EAAC;kBAAQ;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eACzCtB,OAAA;oBAAKiB,SAAS,EAAC,gGAAgG;oBAAAC,QAAA,EAAC;kBAAE;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACzH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,eAGNtB,OAAA;UAAKiB,SAAS,EAAC,eAAe;UAAAC,QAAA,eAC1BlB,OAAA;YAAKiB,SAAS,EAAC,oCAAoC;YAAAC,QAAA,gBAC/ClB,OAAA;cAAIiB,SAAS,EAAC,uCAAuC;cAAAC,QAAA,EAAC;YAAiB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,EAE3Eb,WAAW,gBACRT,OAAA;cAAKiB,SAAS,EAAC,mBAAmB;cAAAC,QAAA,gBAC9BlB,OAAA,CAACF,WAAW;gBAACmB,SAAS,EAAC;cAAuC;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACjEtB,OAAA;gBAAIiB,SAAS,EAAC,uCAAuC;gBAAAC,QAAA,EAAC;cAAa;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACxEtB,OAAA;gBAAGiB,SAAS,EAAC,eAAe;gBAAAC,QAAA,EAAC;cAAuD;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvF,CAAC,gBAENtB,OAAA;cAAKiB,SAAS,EAAC,WAAW;cAAAC,QAAA,gBACtBlB,OAAA;gBAAKiB,SAAS,EAAC,uCAAuC;gBAAAC,QAAA,gBAClDlB,OAAA;kBAAAkB,QAAA,gBACIlB,OAAA;oBAAOwB,OAAO,EAAC,MAAM;oBAACP,SAAS,EAAC,8CAA8C;oBAAAC,QAAA,EAAC;kBAE/E;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eACRtB,OAAA;oBACIyB,IAAI,EAAC,MAAM;oBACXC,EAAE,EAAC,MAAM;oBACTrB,IAAI,EAAC,MAAM;oBACXS,KAAK,EAAEX,QAAQ,CAACE,IAAK;oBACrBsB,QAAQ,EAAEhB,YAAa;oBACvBiB,QAAQ;oBACRX,SAAS,EAAC,gIAAgI;oBAC1IY,WAAW,EAAC;kBAAU;oBAAAV,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACzB,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACD,CAAC,eACNtB,OAAA;kBAAAkB,QAAA,gBACIlB,OAAA;oBAAOwB,OAAO,EAAC,OAAO;oBAACP,SAAS,EAAC,8CAA8C;oBAAAC,QAAA,EAAC;kBAEhF;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eACRtB,OAAA;oBACIyB,IAAI,EAAC,OAAO;oBACZC,EAAE,EAAC,OAAO;oBACVrB,IAAI,EAAC,OAAO;oBACZS,KAAK,EAAEX,QAAQ,CAACG,KAAM;oBACtBqB,QAAQ,EAAEhB,YAAa;oBACvBiB,QAAQ;oBACRX,SAAS,EAAC,gIAAgI;oBAC1IY,WAAW,EAAC;kBAAkB;oBAAAV,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACjC,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACD,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC,eAENtB,OAAA;gBAAAkB,QAAA,gBACIlB,OAAA;kBAAOwB,OAAO,EAAC,SAAS;kBAACP,SAAS,EAAC,8CAA8C;kBAAAC,QAAA,EAAC;gBAElF;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACRtB,OAAA;kBACIyB,IAAI,EAAC,MAAM;kBACXC,EAAE,EAAC,SAAS;kBACZrB,IAAI,EAAC,SAAS;kBACdS,KAAK,EAAEX,QAAQ,CAACI,OAAQ;kBACxBoB,QAAQ,EAAEhB,YAAa;kBACvBiB,QAAQ;kBACRX,SAAS,EAAC,gIAAgI;kBAC1IY,WAAW,EAAC;gBAAsB;kBAAAV,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACrC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD,CAAC,eAENtB,OAAA;gBAAAkB,QAAA,gBACIlB,OAAA;kBAAOwB,OAAO,EAAC,SAAS;kBAACP,SAAS,EAAC,8CAA8C;kBAAAC,QAAA,EAAC;gBAElF;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACRtB,OAAA;kBACI0B,EAAE,EAAC,SAAS;kBACZrB,IAAI,EAAC,SAAS;kBACdyB,IAAI,EAAE,CAAE;kBACRhB,KAAK,EAAEX,QAAQ,CAACK,OAAQ;kBACxBmB,QAAQ,EAAEhB,YAAa;kBACvBiB,QAAQ;kBACRX,SAAS,EAAC,4IAA4I;kBACtJY,WAAW,EAAC;gBAAoC;kBAAAV,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACzC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACX,CAAC,eAENtB,OAAA;gBAAKiB,SAAS,EAAC,mBAAmB;gBAAAC,QAAA,gBAC9BlB,OAAA;kBACI0B,EAAE,EAAC,YAAY;kBACfD,IAAI,EAAC,UAAU;kBACfR,SAAS,EAAC;gBAAmE;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChF,CAAC,eACFtB,OAAA;kBAAOwB,OAAO,EAAC,YAAY;kBAACP,SAAS,EAAC,kCAAkC;kBAAAC,QAAA,EAAC;gBAEzE;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACP,CAAC,eAENtB,OAAA;gBACIyB,IAAI,EAAC,QAAQ;gBACbM,OAAO,EAAEhB,YAAa;gBACtBE,SAAS,EAAC,oMAAoM;gBAAAC,QAAA,gBAE9MlB,OAAA,CAACH,IAAI;kBAACoB,SAAS,EAAC;gBAAS;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eAC5BtB,OAAA;kBAAAkB,QAAA,EAAM;gBAAY;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrB,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACR,CACR;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,eAGNtB,OAAA;QAAKiB,SAAS,EAAC,OAAO;QAAAC,QAAA,eAClBlB,OAAA;UAAKiB,SAAS,EAAC,oCAAoC;UAAAC,QAAA,gBAC/ClB,OAAA;YAAIiB,SAAS,EAAC,mDAAmD;YAAAC,QAAA,EAAC;UAAO;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC9EtB,OAAA;YAAKiB,SAAS,EAAC,8DAA8D;YAAAC,QAAA,eACzElB,OAAA;cAAKiB,SAAS,EAAC,aAAa;cAAAC,QAAA,gBACxBlB,OAAA,CAACL,MAAM;gBAACsB,SAAS,EAAC;cAAsC;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAC3DtB,OAAA;gBAAGiB,SAAS,EAAC,eAAe;gBAAAC,QAAA,EAAC;cAAsC;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,eACvEtB,OAAA;gBAAGiB,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,EAAC;cAAuC;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/E;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;AAEd;AAACpB,EAAA,CA5OQD,OAAO;AAAA+B,EAAA,GAAP/B,OAAO;AA8OhB,eAAeA,OAAO;AAAC,IAAA+B,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}