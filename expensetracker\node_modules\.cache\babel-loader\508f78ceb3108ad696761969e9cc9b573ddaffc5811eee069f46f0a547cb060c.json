{"ast": null, "code": "var _jsxFileName = \"D:\\\\Expense Tracker App (MERN )\\\\expensetracker\\\\src\\\\pages\\\\FinanceDashboard.js\",\n  _s = $RefreshSig$();\nimport { useState } from 'react';\nimport './Dashboard.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction FinanceDashboard() {\n  _s();\n  const [activeTab, setActiveTab] = useState('approved');\n  const [approvedExpenses, setApprovedExpenses] = useState([{\n    id: 1,\n    employee: '<PERSON>',\n    amount: 1200,\n    category: 'Travel',\n    description: 'Flight tickets for business trip',\n    date: '2024-01-15',\n    approvedDate: '2024-01-16',\n    status: 'Approved',\n    receipt: 'receipt_001.pdf'\n  }, {\n    id: 2,\n    employee: '<PERSON>',\n    amount: 850,\n    category: 'Meals',\n    description: 'Client dinner meeting',\n    date: '2024-01-14',\n    approvedDate: '2024-01-15',\n    status: 'Reimbursed',\n    receipt: null,\n    reimbursedDate: '2024-01-17',\n    paymentProof: 'payment_001.pdf'\n  }, {\n    id: 3,\n    employee: '<PERSON>',\n    amount: 2500,\n    category: 'Equipment',\n    description: 'New laptop for development',\n    date: '2024-01-12',\n    approvedDate: '2024-01-13',\n    status: 'Approved',\n    receipt: 'receipt_003.pdf'\n  }]);\n  const [selectedExpense, setSelectedExpense] = useState(null);\n  const [paymentProof, setPaymentProof] = useState(null);\n  const [reimbursementDate, setReimbursementDate] = useState('');\n  const handleMarkReimbursed = expenseId => {\n    if (!reimbursementDate) {\n      alert('Please select reimbursement date');\n      return;\n    }\n    setApprovedExpenses(approvedExpenses.map(exp => exp.id === expenseId ? {\n      ...exp,\n      status: 'Reimbursed',\n      reimbursedDate: reimbursementDate,\n      paymentProof: (paymentProof === null || paymentProof === void 0 ? void 0 : paymentProof.name) || 'payment_proof.pdf'\n    } : exp));\n    setSelectedExpense(null);\n    setPaymentProof(null);\n    setReimbursementDate('');\n    alert('Expense marked as reimbursed successfully!');\n  };\n  const generateReport = type => {\n    alert(`Generating ${type} report...`);\n  };\n  const getStatusColor = status => {\n    switch (status) {\n      case 'Approved':\n        return 'var(--accent-tertiary)';\n      case 'Reimbursed':\n        return 'var(--accent-primary)';\n      default:\n        return 'var(--text-secondary)';\n    }\n  };\n  const totalApproved = approvedExpenses.filter(exp => exp.status === 'Approved').reduce((sum, exp) => sum + exp.amount, 0);\n  const totalReimbursed = approvedExpenses.filter(exp => exp.status === 'Reimbursed').reduce((sum, exp) => sum + exp.amount, 0);\n  const tabs = [{\n    id: 'approved',\n    label: 'Approved Expenses',\n    icon: '✅'\n  }, {\n    id: 'reimbursed',\n    label: 'Reimbursed',\n    icon: '💰'\n  }, {\n    id: 'reports',\n    label: 'Reports',\n    icon: '📊'\n  }, {\n    id: 'receipts',\n    label: 'Receipt Verification',\n    icon: '📄'\n  }];\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"dashboard-container\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"dashboard-background\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"dashboard-particles\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 98,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 97,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"dashboard-content\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"dashboard-header\",\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          className: \"dashboard-title\",\n          children: [\"Finance \", /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"gradient-text\",\n            children: \"Dashboard\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 104,\n            columnNumber: 33\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 103,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"dashboard-subtitle\",\n          children: \"Process reimbursements and manage financial records\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 106,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 102,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"dashboard-stats\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"stat-card\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"stat-icon\",\n            children: \"\\u23F3\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 111,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"stat-info\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"stat-value\",\n              children: [\"\\u20B9\", totalApproved.toLocaleString()]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 113,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"stat-label\",\n              children: \"Pending Reimbursement\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 114,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 112,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 110,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"stat-card\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"stat-icon\",\n            children: \"\\uD83D\\uDCB0\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 118,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"stat-info\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"stat-value\",\n              children: [\"\\u20B9\", totalReimbursed.toLocaleString()]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 120,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"stat-label\",\n              children: \"Total Reimbursed\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 121,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 119,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 117,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"stat-card\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"stat-icon\",\n            children: \"\\uD83D\\uDCCA\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 125,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"stat-info\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"stat-value\",\n              children: approvedExpenses.length\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 127,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"stat-label\",\n              children: \"Total Expenses\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 128,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 126,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 124,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"stat-card\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"stat-icon\",\n            children: \"\\uD83D\\uDCC5\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 132,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"stat-info\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"stat-value\",\n              children: \"Jan 2024\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 134,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"stat-label\",\n              children: \"Current Period\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 135,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 133,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 131,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 109,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"dashboard-tabs\",\n        children: tabs.map(tab => /*#__PURE__*/_jsxDEV(\"button\", {\n          className: `tab-btn ${activeTab === tab.id ? 'active' : ''}`,\n          onClick: () => setActiveTab(tab.id),\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"tab-icon\",\n            children: tab.icon\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 147,\n            columnNumber: 29\n          }, this), tab.label]\n        }, tab.id, true, {\n          fileName: _jsxFileName,\n          lineNumber: 142,\n          columnNumber: 25\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 140,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"dashboard-main\",\n        children: [activeTab === 'approved' && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"tab-content\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"content-card\",\n            children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n              className: \"card-title\",\n              children: \"Approved Expenses Awaiting Reimbursement\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 157,\n              columnNumber: 33\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"expenses-table\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"table-header\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  children: \"Employee\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 160,\n                  columnNumber: 41\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  children: \"Amount\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 161,\n                  columnNumber: 41\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  children: \"Category\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 162,\n                  columnNumber: 41\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  children: \"Approved Date\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 163,\n                  columnNumber: 41\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  children: \"Status\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 164,\n                  columnNumber: 41\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  children: \"Actions\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 165,\n                  columnNumber: 41\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 159,\n                columnNumber: 37\n              }, this), approvedExpenses.filter(exp => exp.status === 'Approved').map(expense => /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"table-row\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"employee-name\",\n                  children: expense.employee\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 169,\n                  columnNumber: 45\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"expense-amount\",\n                  children: [\"\\u20B9\", expense.amount]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 170,\n                  columnNumber: 45\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"expense-category\",\n                  children: expense.category\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 171,\n                  columnNumber: 45\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"expense-date\",\n                  children: expense.approvedDate\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 172,\n                  columnNumber: 45\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"expense-status\",\n                  style: {\n                    color: getStatusColor(expense.status)\n                  },\n                  children: expense.status\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 173,\n                  columnNumber: 45\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"table-actions\",\n                  children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                    className: \"action-btn reimburse-btn\",\n                    onClick: () => setSelectedExpense(expense),\n                    children: \"\\uD83D\\uDCB0 Mark Reimbursed\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 180,\n                    columnNumber: 49\n                  }, this), expense.receipt && /*#__PURE__*/_jsxDEV(\"button\", {\n                    className: \"action-btn view-btn\",\n                    children: \"\\uD83D\\uDCC4 View Receipt\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 187,\n                    columnNumber: 53\n                  }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                    className: \"action-btn details-btn\",\n                    children: \"\\uD83D\\uDC41\\uFE0F Details\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 189,\n                    columnNumber: 49\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 179,\n                  columnNumber: 45\n                }, this)]\n              }, expense.id, true, {\n                fileName: _jsxFileName,\n                lineNumber: 168,\n                columnNumber: 41\n              }, this))]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 158,\n              columnNumber: 33\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 156,\n            columnNumber: 29\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 155,\n          columnNumber: 25\n        }, this), activeTab === 'reimbursed' && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"tab-content\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"content-card\",\n            children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n              className: \"card-title\",\n              children: \"Reimbursed Expenses\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 201,\n              columnNumber: 33\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"reimbursed-list\",\n              children: approvedExpenses.filter(exp => exp.status === 'Reimbursed').map(expense => /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"reimbursed-item\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"reimbursed-info\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"reimbursed-header\",\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"employee-name\",\n                      children: expense.employee\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 207,\n                      columnNumber: 53\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"reimbursed-amount\",\n                      children: [\"\\u20B9\", expense.amount]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 208,\n                      columnNumber: 53\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 206,\n                    columnNumber: 49\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"reimbursed-details\",\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"expense-category\",\n                      children: expense.category\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 211,\n                      columnNumber: 53\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"reimbursed-date\",\n                      children: [\"Reimbursed: \", expense.reimbursedDate]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 212,\n                      columnNumber: 53\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 210,\n                    columnNumber: 49\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"expense-description\",\n                    children: expense.description\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 214,\n                    columnNumber: 49\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 205,\n                  columnNumber: 45\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"reimbursed-actions\",\n                  children: [expense.paymentProof && /*#__PURE__*/_jsxDEV(\"button\", {\n                    className: \"action-btn download-btn\",\n                    children: \"\\uD83D\\uDCE5 Payment Proof\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 218,\n                    columnNumber: 53\n                  }, this), expense.receipt && /*#__PURE__*/_jsxDEV(\"button\", {\n                    className: \"action-btn view-btn\",\n                    children: \"\\uD83D\\uDCC4 Receipt\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 221,\n                    columnNumber: 53\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 216,\n                  columnNumber: 45\n                }, this)]\n              }, expense.id, true, {\n                fileName: _jsxFileName,\n                lineNumber: 204,\n                columnNumber: 41\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 202,\n              columnNumber: 33\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 200,\n            columnNumber: 29\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 199,\n          columnNumber: 25\n        }, this), activeTab === 'reports' && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"tab-content\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"content-card\",\n            children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n              className: \"card-title\",\n              children: \"Generate Reports\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 234,\n              columnNumber: 33\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"reports-grid\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"report-card\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"report-icon\",\n                  children: \"\\uD83D\\uDCCA\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 237,\n                  columnNumber: 41\n                }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                  className: \"report-title\",\n                  children: \"Monthly Report\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 238,\n                  columnNumber: 41\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"report-description\",\n                  children: \"Generate monthly reimbursement summary by employee and category\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 239,\n                  columnNumber: 41\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  className: \"report-btn\",\n                  onClick: () => generateReport('Monthly'),\n                  children: \"\\uD83D\\uDCE5 Generate Monthly\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 242,\n                  columnNumber: 41\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 236,\n                columnNumber: 37\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"report-card\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"report-icon\",\n                  children: \"\\uD83D\\uDCC8\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 250,\n                  columnNumber: 41\n                }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                  className: \"report-title\",\n                  children: \"Quarterly Report\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 251,\n                  columnNumber: 41\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"report-description\",\n                  children: \"Comprehensive quarterly analysis with trends and insights\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 252,\n                  columnNumber: 41\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  className: \"report-btn\",\n                  onClick: () => generateReport('Quarterly'),\n                  children: \"\\uD83D\\uDCE5 Generate Quarterly\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 255,\n                  columnNumber: 41\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 249,\n                columnNumber: 37\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"report-card\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"report-icon\",\n                  children: \"\\uD83D\\uDC65\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 263,\n                  columnNumber: 41\n                }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                  className: \"report-title\",\n                  children: \"Employee Report\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 264,\n                  columnNumber: 41\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"report-description\",\n                  children: \"Individual employee expense breakdown and patterns\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 265,\n                  columnNumber: 41\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  className: \"report-btn\",\n                  onClick: () => generateReport('Employee'),\n                  children: \"\\uD83D\\uDCE5 Generate by Employee\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 268,\n                  columnNumber: 41\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 262,\n                columnNumber: 37\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"report-card\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"report-icon\",\n                  children: \"\\uD83C\\uDFF7\\uFE0F\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 276,\n                  columnNumber: 41\n                }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                  className: \"report-title\",\n                  children: \"Category Report\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 277,\n                  columnNumber: 41\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"report-description\",\n                  children: \"Expense analysis by category with budget comparisons\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 278,\n                  columnNumber: 41\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  className: \"report-btn\",\n                  onClick: () => generateReport('Category'),\n                  children: \"\\uD83D\\uDCE5 Generate by Category\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 281,\n                  columnNumber: 41\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 275,\n                columnNumber: 37\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 235,\n              columnNumber: 33\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 233,\n            columnNumber: 29\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 232,\n          columnNumber: 25\n        }, this), activeTab === 'receipts' && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"tab-content\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"content-card\",\n            children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n              className: \"card-title\",\n              children: \"Receipt Verification\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 296,\n              columnNumber: 33\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"receipts-list\",\n              children: approvedExpenses.filter(exp => exp.receipt).map(expense => /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"receipt-item\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"receipt-info\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"receipt-header\",\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"employee-name\",\n                      children: expense.employee\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 302,\n                      columnNumber: 53\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"receipt-amount\",\n                      children: [\"\\u20B9\", expense.amount]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 303,\n                      columnNumber: 53\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 301,\n                    columnNumber: 49\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"receipt-details\",\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"expense-category\",\n                      children: expense.category\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 306,\n                      columnNumber: 53\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"receipt-file\",\n                      children: expense.receipt\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 307,\n                      columnNumber: 53\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 305,\n                    columnNumber: 49\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"expense-description\",\n                    children: expense.description\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 309,\n                    columnNumber: 49\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 300,\n                  columnNumber: 45\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"receipt-actions\",\n                  children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                    className: \"action-btn download-btn\",\n                    children: \"\\uD83D\\uDCE5 Download\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 312,\n                    columnNumber: 49\n                  }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                    className: \"action-btn view-btn\",\n                    children: \"\\uD83D\\uDC41\\uFE0F View\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 313,\n                    columnNumber: 49\n                  }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                    className: \"action-btn verify-btn\",\n                    children: \"\\u2705 Verify\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 314,\n                    columnNumber: 49\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 311,\n                  columnNumber: 45\n                }, this)]\n              }, expense.id, true, {\n                fileName: _jsxFileName,\n                lineNumber: 299,\n                columnNumber: 41\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 297,\n              columnNumber: 33\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 295,\n            columnNumber: 29\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 294,\n          columnNumber: 25\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 153,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 101,\n      columnNumber: 13\n    }, this), selectedExpense && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"modal-overlay\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"modal-content\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          children: \"Mark as Reimbursed\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 329,\n          columnNumber: 25\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: [\"Processing reimbursement for \", selectedExpense.employee, \" - \\u20B9\", selectedExpense.amount]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 330,\n          columnNumber: 25\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-group\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            className: \"form-label\",\n            children: \"Reimbursement Date\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 333,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"date\",\n            className: \"form-input\",\n            value: reimbursementDate,\n            onChange: e => setReimbursementDate(e.target.value)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 334,\n            columnNumber: 29\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 332,\n          columnNumber: 25\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-group\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            className: \"form-label\",\n            children: \"Payment Proof (Optional)\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 343,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"file\",\n            className: \"form-input file-input\",\n            accept: \".pdf,.jpg,.jpeg,.png\",\n            onChange: e => setPaymentProof(e.target.files[0])\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 344,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(\"small\", {\n            className: \"form-help\",\n            children: \"Upload bank transfer receipt or payment confirmation\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 350,\n            columnNumber: 29\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 342,\n          columnNumber: 25\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"modal-actions\",\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"action-btn reimburse-btn\",\n            onClick: () => handleMarkReimbursed(selectedExpense.id),\n            children: \"\\uD83D\\uDCB0 Mark Reimbursed\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 354,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"action-btn cancel-btn\",\n            onClick: () => setSelectedExpense(null),\n            children: \"Cancel\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 360,\n            columnNumber: 29\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 353,\n          columnNumber: 25\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 328,\n        columnNumber: 21\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 327,\n      columnNumber: 17\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 96,\n    columnNumber: 9\n  }, this);\n}\n_s(FinanceDashboard, \"10smy4sT3LpX444JkwgnIo2cXhA=\");\n_c = FinanceDashboard;\nexport default FinanceDashboard;\nvar _c;\n$RefreshReg$(_c, \"FinanceDashboard\");", "map": {"version": 3, "names": ["useState", "jsxDEV", "_jsxDEV", "FinanceDashboard", "_s", "activeTab", "setActiveTab", "approvedExpenses", "setApprovedExpenses", "id", "employee", "amount", "category", "description", "date", "approvedDate", "status", "receipt", "reimbursedDate", "paymentProof", "selectedExpense", "setSelectedExpense", "setPaymentProof", "reimbursementDate", "setReimbursementDate", "handleMarkReimbursed", "expenseId", "alert", "map", "exp", "name", "generateReport", "type", "getStatusColor", "totalApproved", "filter", "reduce", "sum", "totalReimbursed", "tabs", "label", "icon", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "toLocaleString", "length", "tab", "onClick", "expense", "style", "color", "value", "onChange", "e", "target", "accept", "files", "_c", "$RefreshReg$"], "sources": ["D:/Expense Tracker App (MERN )/expensetracker/src/pages/FinanceDashboard.js"], "sourcesContent": ["import { useState } from 'react';\nimport './Dashboard.css';\n\nfunction FinanceDashboard() {\n    const [activeTab, setActiveTab] = useState('approved');\n    const [approvedExpenses, setApprovedExpenses] = useState([\n        {\n            id: 1,\n            employee: '<PERSON>',\n            amount: 1200,\n            category: 'Travel',\n            description: 'Flight tickets for business trip',\n            date: '2024-01-15',\n            approvedDate: '2024-01-16',\n            status: 'Approved',\n            receipt: 'receipt_001.pdf'\n        },\n        {\n            id: 2,\n            employee: '<PERSON>',\n            amount: 850,\n            category: 'Meals',\n            description: 'Client dinner meeting',\n            date: '2024-01-14',\n            approvedDate: '2024-01-15',\n            status: 'Reimbursed',\n            receipt: null,\n            reimbursedDate: '2024-01-17',\n            paymentProof: 'payment_001.pdf'\n        },\n        {\n            id: 3,\n            employee: '<PERSON>',\n            amount: 2500,\n            category: 'Equipment',\n            description: 'New laptop for development',\n            date: '2024-01-12',\n            approvedDate: '2024-01-13',\n            status: 'Approved',\n            receipt: 'receipt_003.pdf'\n        }\n    ]);\n\n    const [selectedExpense, setSelectedExpense] = useState(null);\n    const [paymentProof, setPaymentProof] = useState(null);\n    const [reimbursementDate, setReimbursementDate] = useState('');\n\n    const handleMarkReimbursed = (expenseId) => {\n        if (!reimbursementDate) {\n            alert('Please select reimbursement date');\n            return;\n        }\n        \n        setApprovedExpenses(approvedExpenses.map(exp => \n            exp.id === expenseId ? { \n                ...exp, \n                status: 'Reimbursed', \n                reimbursedDate: reimbursementDate,\n                paymentProof: paymentProof?.name || 'payment_proof.pdf'\n            } : exp\n        ));\n        setSelectedExpense(null);\n        setPaymentProof(null);\n        setReimbursementDate('');\n        alert('Expense marked as reimbursed successfully!');\n    };\n\n    const generateReport = (type) => {\n        alert(`Generating ${type} report...`);\n    };\n\n    const getStatusColor = (status) => {\n        switch (status) {\n            case 'Approved': return 'var(--accent-tertiary)';\n            case 'Reimbursed': return 'var(--accent-primary)';\n            default: return 'var(--text-secondary)';\n        }\n    };\n\n    const totalApproved = approvedExpenses\n        .filter(exp => exp.status === 'Approved')\n        .reduce((sum, exp) => sum + exp.amount, 0);\n\n    const totalReimbursed = approvedExpenses\n        .filter(exp => exp.status === 'Reimbursed')\n        .reduce((sum, exp) => sum + exp.amount, 0);\n\n    const tabs = [\n        { id: 'approved', label: 'Approved Expenses', icon: '✅' },\n        { id: 'reimbursed', label: 'Reimbursed', icon: '💰' },\n        { id: 'reports', label: 'Reports', icon: '📊' },\n        { id: 'receipts', label: 'Receipt Verification', icon: '📄' }\n    ];\n\n    return (\n        <div className=\"dashboard-container\">\n            <div className=\"dashboard-background\">\n                <div className=\"dashboard-particles\"></div>\n            </div>\n\n            <div className=\"dashboard-content\">\n                <div className=\"dashboard-header\">\n                    <h1 className=\"dashboard-title\">\n                        Finance <span className=\"gradient-text\">Dashboard</span>\n                    </h1>\n                    <p className=\"dashboard-subtitle\">Process reimbursements and manage financial records</p>\n                </div>\n\n                <div className=\"dashboard-stats\">\n                    <div className=\"stat-card\">\n                        <div className=\"stat-icon\">⏳</div>\n                        <div className=\"stat-info\">\n                            <span className=\"stat-value\">₹{totalApproved.toLocaleString()}</span>\n                            <span className=\"stat-label\">Pending Reimbursement</span>\n                        </div>\n                    </div>\n                    <div className=\"stat-card\">\n                        <div className=\"stat-icon\">💰</div>\n                        <div className=\"stat-info\">\n                            <span className=\"stat-value\">₹{totalReimbursed.toLocaleString()}</span>\n                            <span className=\"stat-label\">Total Reimbursed</span>\n                        </div>\n                    </div>\n                    <div className=\"stat-card\">\n                        <div className=\"stat-icon\">📊</div>\n                        <div className=\"stat-info\">\n                            <span className=\"stat-value\">{approvedExpenses.length}</span>\n                            <span className=\"stat-label\">Total Expenses</span>\n                        </div>\n                    </div>\n                    <div className=\"stat-card\">\n                        <div className=\"stat-icon\">📅</div>\n                        <div className=\"stat-info\">\n                            <span className=\"stat-value\">Jan 2024</span>\n                            <span className=\"stat-label\">Current Period</span>\n                        </div>\n                    </div>\n                </div>\n\n                <div className=\"dashboard-tabs\">\n                    {tabs.map(tab => (\n                        <button\n                            key={tab.id}\n                            className={`tab-btn ${activeTab === tab.id ? 'active' : ''}`}\n                            onClick={() => setActiveTab(tab.id)}\n                        >\n                            <span className=\"tab-icon\">{tab.icon}</span>\n                            {tab.label}\n                        </button>\n                    ))}\n                </div>\n\n                <div className=\"dashboard-main\">\n                    {activeTab === 'approved' && (\n                        <div className=\"tab-content\">\n                            <div className=\"content-card\">\n                                <h2 className=\"card-title\">Approved Expenses Awaiting Reimbursement</h2>\n                                <div className=\"expenses-table\">\n                                    <div className=\"table-header\">\n                                        <span>Employee</span>\n                                        <span>Amount</span>\n                                        <span>Category</span>\n                                        <span>Approved Date</span>\n                                        <span>Status</span>\n                                        <span>Actions</span>\n                                    </div>\n                                    {approvedExpenses.filter(exp => exp.status === 'Approved').map(expense => (\n                                        <div key={expense.id} className=\"table-row\">\n                                            <span className=\"employee-name\">{expense.employee}</span>\n                                            <span className=\"expense-amount\">₹{expense.amount}</span>\n                                            <span className=\"expense-category\">{expense.category}</span>\n                                            <span className=\"expense-date\">{expense.approvedDate}</span>\n                                            <span \n                                                className=\"expense-status\"\n                                                style={{ color: getStatusColor(expense.status) }}\n                                            >\n                                                {expense.status}\n                                            </span>\n                                            <div className=\"table-actions\">\n                                                <button \n                                                    className=\"action-btn reimburse-btn\"\n                                                    onClick={() => setSelectedExpense(expense)}\n                                                >\n                                                    💰 Mark Reimbursed\n                                                </button>\n                                                {expense.receipt && (\n                                                    <button className=\"action-btn view-btn\">📄 View Receipt</button>\n                                                )}\n                                                <button className=\"action-btn details-btn\">👁️ Details</button>\n                                            </div>\n                                        </div>\n                                    ))}\n                                </div>\n                            </div>\n                        </div>\n                    )}\n\n                    {activeTab === 'reimbursed' && (\n                        <div className=\"tab-content\">\n                            <div className=\"content-card\">\n                                <h2 className=\"card-title\">Reimbursed Expenses</h2>\n                                <div className=\"reimbursed-list\">\n                                    {approvedExpenses.filter(exp => exp.status === 'Reimbursed').map(expense => (\n                                        <div key={expense.id} className=\"reimbursed-item\">\n                                            <div className=\"reimbursed-info\">\n                                                <div className=\"reimbursed-header\">\n                                                    <span className=\"employee-name\">{expense.employee}</span>\n                                                    <span className=\"reimbursed-amount\">₹{expense.amount}</span>\n                                                </div>\n                                                <div className=\"reimbursed-details\">\n                                                    <span className=\"expense-category\">{expense.category}</span>\n                                                    <span className=\"reimbursed-date\">Reimbursed: {expense.reimbursedDate}</span>\n                                                </div>\n                                                <p className=\"expense-description\">{expense.description}</p>\n                                            </div>\n                                            <div className=\"reimbursed-actions\">\n                                                {expense.paymentProof && (\n                                                    <button className=\"action-btn download-btn\">📥 Payment Proof</button>\n                                                )}\n                                                {expense.receipt && (\n                                                    <button className=\"action-btn view-btn\">📄 Receipt</button>\n                                                )}\n                                            </div>\n                                        </div>\n                                    ))}\n                                </div>\n                            </div>\n                        </div>\n                    )}\n\n                    {activeTab === 'reports' && (\n                        <div className=\"tab-content\">\n                            <div className=\"content-card\">\n                                <h2 className=\"card-title\">Generate Reports</h2>\n                                <div className=\"reports-grid\">\n                                    <div className=\"report-card\">\n                                        <div className=\"report-icon\">📊</div>\n                                        <h3 className=\"report-title\">Monthly Report</h3>\n                                        <p className=\"report-description\">\n                                            Generate monthly reimbursement summary by employee and category\n                                        </p>\n                                        <button \n                                            className=\"report-btn\"\n                                            onClick={() => generateReport('Monthly')}\n                                        >\n                                            📥 Generate Monthly\n                                        </button>\n                                    </div>\n                                    <div className=\"report-card\">\n                                        <div className=\"report-icon\">📈</div>\n                                        <h3 className=\"report-title\">Quarterly Report</h3>\n                                        <p className=\"report-description\">\n                                            Comprehensive quarterly analysis with trends and insights\n                                        </p>\n                                        <button \n                                            className=\"report-btn\"\n                                            onClick={() => generateReport('Quarterly')}\n                                        >\n                                            📥 Generate Quarterly\n                                        </button>\n                                    </div>\n                                    <div className=\"report-card\">\n                                        <div className=\"report-icon\">👥</div>\n                                        <h3 className=\"report-title\">Employee Report</h3>\n                                        <p className=\"report-description\">\n                                            Individual employee expense breakdown and patterns\n                                        </p>\n                                        <button \n                                            className=\"report-btn\"\n                                            onClick={() => generateReport('Employee')}\n                                        >\n                                            📥 Generate by Employee\n                                        </button>\n                                    </div>\n                                    <div className=\"report-card\">\n                                        <div className=\"report-icon\">🏷️</div>\n                                        <h3 className=\"report-title\">Category Report</h3>\n                                        <p className=\"report-description\">\n                                            Expense analysis by category with budget comparisons\n                                        </p>\n                                        <button \n                                            className=\"report-btn\"\n                                            onClick={() => generateReport('Category')}\n                                        >\n                                            📥 Generate by Category\n                                        </button>\n                                    </div>\n                                </div>\n                            </div>\n                        </div>\n                    )}\n\n                    {activeTab === 'receipts' && (\n                        <div className=\"tab-content\">\n                            <div className=\"content-card\">\n                                <h2 className=\"card-title\">Receipt Verification</h2>\n                                <div className=\"receipts-list\">\n                                    {approvedExpenses.filter(exp => exp.receipt).map(expense => (\n                                        <div key={expense.id} className=\"receipt-item\">\n                                            <div className=\"receipt-info\">\n                                                <div className=\"receipt-header\">\n                                                    <span className=\"employee-name\">{expense.employee}</span>\n                                                    <span className=\"receipt-amount\">₹{expense.amount}</span>\n                                                </div>\n                                                <div className=\"receipt-details\">\n                                                    <span className=\"expense-category\">{expense.category}</span>\n                                                    <span className=\"receipt-file\">{expense.receipt}</span>\n                                                </div>\n                                                <p className=\"expense-description\">{expense.description}</p>\n                                            </div>\n                                            <div className=\"receipt-actions\">\n                                                <button className=\"action-btn download-btn\">📥 Download</button>\n                                                <button className=\"action-btn view-btn\">👁️ View</button>\n                                                <button className=\"action-btn verify-btn\">✅ Verify</button>\n                                            </div>\n                                        </div>\n                                    ))}\n                                </div>\n                            </div>\n                        </div>\n                    )}\n                </div>\n            </div>\n\n            {/* Reimbursement Modal */}\n            {selectedExpense && (\n                <div className=\"modal-overlay\">\n                    <div className=\"modal-content\">\n                        <h3>Mark as Reimbursed</h3>\n                        <p>Processing reimbursement for {selectedExpense.employee} - ₹{selectedExpense.amount}</p>\n                        \n                        <div className=\"form-group\">\n                            <label className=\"form-label\">Reimbursement Date</label>\n                            <input\n                                type=\"date\"\n                                className=\"form-input\"\n                                value={reimbursementDate}\n                                onChange={(e) => setReimbursementDate(e.target.value)}\n                            />\n                        </div>\n\n                        <div className=\"form-group\">\n                            <label className=\"form-label\">Payment Proof (Optional)</label>\n                            <input\n                                type=\"file\"\n                                className=\"form-input file-input\"\n                                accept=\".pdf,.jpg,.jpeg,.png\"\n                                onChange={(e) => setPaymentProof(e.target.files[0])}\n                            />\n                            <small className=\"form-help\">Upload bank transfer receipt or payment confirmation</small>\n                        </div>\n\n                        <div className=\"modal-actions\">\n                            <button \n                                className=\"action-btn reimburse-btn\"\n                                onClick={() => handleMarkReimbursed(selectedExpense.id)}\n                            >\n                                💰 Mark Reimbursed\n                            </button>\n                            <button \n                                className=\"action-btn cancel-btn\"\n                                onClick={() => setSelectedExpense(null)}\n                            >\n                                Cancel\n                            </button>\n                        </div>\n                    </div>\n                </div>\n            )}\n        </div>\n    );\n}\n\nexport default FinanceDashboard;\n"], "mappings": ";;AAAA,SAASA,QAAQ,QAAQ,OAAO;AAChC,OAAO,iBAAiB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEzB,SAASC,gBAAgBA,CAAA,EAAG;EAAAC,EAAA;EACxB,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAGN,QAAQ,CAAC,UAAU,CAAC;EACtD,MAAM,CAACO,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGR,QAAQ,CAAC,CACrD;IACIS,EAAE,EAAE,CAAC;IACLC,QAAQ,EAAE,UAAU;IACpBC,MAAM,EAAE,IAAI;IACZC,QAAQ,EAAE,QAAQ;IAClBC,WAAW,EAAE,kCAAkC;IAC/CC,IAAI,EAAE,YAAY;IAClBC,YAAY,EAAE,YAAY;IAC1BC,MAAM,EAAE,UAAU;IAClBC,OAAO,EAAE;EACb,CAAC,EACD;IACIR,EAAE,EAAE,CAAC;IACLC,QAAQ,EAAE,YAAY;IACtBC,MAAM,EAAE,GAAG;IACXC,QAAQ,EAAE,OAAO;IACjBC,WAAW,EAAE,uBAAuB;IACpCC,IAAI,EAAE,YAAY;IAClBC,YAAY,EAAE,YAAY;IAC1BC,MAAM,EAAE,YAAY;IACpBC,OAAO,EAAE,IAAI;IACbC,cAAc,EAAE,YAAY;IAC5BC,YAAY,EAAE;EAClB,CAAC,EACD;IACIV,EAAE,EAAE,CAAC;IACLC,QAAQ,EAAE,cAAc;IACxBC,MAAM,EAAE,IAAI;IACZC,QAAQ,EAAE,WAAW;IACrBC,WAAW,EAAE,4BAA4B;IACzCC,IAAI,EAAE,YAAY;IAClBC,YAAY,EAAE,YAAY;IAC1BC,MAAM,EAAE,UAAU;IAClBC,OAAO,EAAE;EACb,CAAC,CACJ,CAAC;EAEF,MAAM,CAACG,eAAe,EAAEC,kBAAkB,CAAC,GAAGrB,QAAQ,CAAC,IAAI,CAAC;EAC5D,MAAM,CAACmB,YAAY,EAAEG,eAAe,CAAC,GAAGtB,QAAQ,CAAC,IAAI,CAAC;EACtD,MAAM,CAACuB,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGxB,QAAQ,CAAC,EAAE,CAAC;EAE9D,MAAMyB,oBAAoB,GAAIC,SAAS,IAAK;IACxC,IAAI,CAACH,iBAAiB,EAAE;MACpBI,KAAK,CAAC,kCAAkC,CAAC;MACzC;IACJ;IAEAnB,mBAAmB,CAACD,gBAAgB,CAACqB,GAAG,CAACC,GAAG,IACxCA,GAAG,CAACpB,EAAE,KAAKiB,SAAS,GAAG;MACnB,GAAGG,GAAG;MACNb,MAAM,EAAE,YAAY;MACpBE,cAAc,EAAEK,iBAAiB;MACjCJ,YAAY,EAAE,CAAAA,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAEW,IAAI,KAAI;IACxC,CAAC,GAAGD,GACR,CAAC,CAAC;IACFR,kBAAkB,CAAC,IAAI,CAAC;IACxBC,eAAe,CAAC,IAAI,CAAC;IACrBE,oBAAoB,CAAC,EAAE,CAAC;IACxBG,KAAK,CAAC,4CAA4C,CAAC;EACvD,CAAC;EAED,MAAMI,cAAc,GAAIC,IAAI,IAAK;IAC7BL,KAAK,CAAC,cAAcK,IAAI,YAAY,CAAC;EACzC,CAAC;EAED,MAAMC,cAAc,GAAIjB,MAAM,IAAK;IAC/B,QAAQA,MAAM;MACV,KAAK,UAAU;QAAE,OAAO,wBAAwB;MAChD,KAAK,YAAY;QAAE,OAAO,uBAAuB;MACjD;QAAS,OAAO,uBAAuB;IAC3C;EACJ,CAAC;EAED,MAAMkB,aAAa,GAAG3B,gBAAgB,CACjC4B,MAAM,CAACN,GAAG,IAAIA,GAAG,CAACb,MAAM,KAAK,UAAU,CAAC,CACxCoB,MAAM,CAAC,CAACC,GAAG,EAAER,GAAG,KAAKQ,GAAG,GAAGR,GAAG,CAAClB,MAAM,EAAE,CAAC,CAAC;EAE9C,MAAM2B,eAAe,GAAG/B,gBAAgB,CACnC4B,MAAM,CAACN,GAAG,IAAIA,GAAG,CAACb,MAAM,KAAK,YAAY,CAAC,CAC1CoB,MAAM,CAAC,CAACC,GAAG,EAAER,GAAG,KAAKQ,GAAG,GAAGR,GAAG,CAAClB,MAAM,EAAE,CAAC,CAAC;EAE9C,MAAM4B,IAAI,GAAG,CACT;IAAE9B,EAAE,EAAE,UAAU;IAAE+B,KAAK,EAAE,mBAAmB;IAAEC,IAAI,EAAE;EAAI,CAAC,EACzD;IAAEhC,EAAE,EAAE,YAAY;IAAE+B,KAAK,EAAE,YAAY;IAAEC,IAAI,EAAE;EAAK,CAAC,EACrD;IAAEhC,EAAE,EAAE,SAAS;IAAE+B,KAAK,EAAE,SAAS;IAAEC,IAAI,EAAE;EAAK,CAAC,EAC/C;IAAEhC,EAAE,EAAE,UAAU;IAAE+B,KAAK,EAAE,sBAAsB;IAAEC,IAAI,EAAE;EAAK,CAAC,CAChE;EAED,oBACIvC,OAAA;IAAKwC,SAAS,EAAC,qBAAqB;IAAAC,QAAA,gBAChCzC,OAAA;MAAKwC,SAAS,EAAC,sBAAsB;MAAAC,QAAA,eACjCzC,OAAA;QAAKwC,SAAS,EAAC;MAAqB;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC1C,CAAC,eAEN7C,OAAA;MAAKwC,SAAS,EAAC,mBAAmB;MAAAC,QAAA,gBAC9BzC,OAAA;QAAKwC,SAAS,EAAC,kBAAkB;QAAAC,QAAA,gBAC7BzC,OAAA;UAAIwC,SAAS,EAAC,iBAAiB;UAAAC,QAAA,GAAC,UACpB,eAAAzC,OAAA;YAAMwC,SAAS,EAAC,eAAe;YAAAC,QAAA,EAAC;UAAS;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxD,CAAC,eACL7C,OAAA;UAAGwC,SAAS,EAAC,oBAAoB;UAAAC,QAAA,EAAC;QAAmD;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACxF,CAAC,eAEN7C,OAAA;QAAKwC,SAAS,EAAC,iBAAiB;QAAAC,QAAA,gBAC5BzC,OAAA;UAAKwC,SAAS,EAAC,WAAW;UAAAC,QAAA,gBACtBzC,OAAA;YAAKwC,SAAS,EAAC,WAAW;YAAAC,QAAA,EAAC;UAAC;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eAClC7C,OAAA;YAAKwC,SAAS,EAAC,WAAW;YAAAC,QAAA,gBACtBzC,OAAA;cAAMwC,SAAS,EAAC,YAAY;cAAAC,QAAA,GAAC,QAAC,EAACT,aAAa,CAACc,cAAc,CAAC,CAAC;YAAA;cAAAJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACrE7C,OAAA;cAAMwC,SAAS,EAAC,YAAY;cAAAC,QAAA,EAAC;YAAqB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,eACN7C,OAAA;UAAKwC,SAAS,EAAC,WAAW;UAAAC,QAAA,gBACtBzC,OAAA;YAAKwC,SAAS,EAAC,WAAW;YAAAC,QAAA,EAAC;UAAE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACnC7C,OAAA;YAAKwC,SAAS,EAAC,WAAW;YAAAC,QAAA,gBACtBzC,OAAA;cAAMwC,SAAS,EAAC,YAAY;cAAAC,QAAA,GAAC,QAAC,EAACL,eAAe,CAACU,cAAc,CAAC,CAAC;YAAA;cAAAJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACvE7C,OAAA;cAAMwC,SAAS,EAAC,YAAY;cAAAC,QAAA,EAAC;YAAgB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,eACN7C,OAAA;UAAKwC,SAAS,EAAC,WAAW;UAAAC,QAAA,gBACtBzC,OAAA;YAAKwC,SAAS,EAAC,WAAW;YAAAC,QAAA,EAAC;UAAE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACnC7C,OAAA;YAAKwC,SAAS,EAAC,WAAW;YAAAC,QAAA,gBACtBzC,OAAA;cAAMwC,SAAS,EAAC,YAAY;cAAAC,QAAA,EAAEpC,gBAAgB,CAAC0C;YAAM;cAAAL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eAC7D7C,OAAA;cAAMwC,SAAS,EAAC,YAAY;cAAAC,QAAA,EAAC;YAAc;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,eACN7C,OAAA;UAAKwC,SAAS,EAAC,WAAW;UAAAC,QAAA,gBACtBzC,OAAA;YAAKwC,SAAS,EAAC,WAAW;YAAAC,QAAA,EAAC;UAAE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACnC7C,OAAA;YAAKwC,SAAS,EAAC,WAAW;YAAAC,QAAA,gBACtBzC,OAAA;cAAMwC,SAAS,EAAC,YAAY;cAAAC,QAAA,EAAC;YAAQ;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAC5C7C,OAAA;cAAMwC,SAAS,EAAC,YAAY;cAAAC,QAAA,EAAC;YAAc;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,eAEN7C,OAAA;QAAKwC,SAAS,EAAC,gBAAgB;QAAAC,QAAA,EAC1BJ,IAAI,CAACX,GAAG,CAACsB,GAAG,iBACThD,OAAA;UAEIwC,SAAS,EAAE,WAAWrC,SAAS,KAAK6C,GAAG,CAACzC,EAAE,GAAG,QAAQ,GAAG,EAAE,EAAG;UAC7D0C,OAAO,EAAEA,CAAA,KAAM7C,YAAY,CAAC4C,GAAG,CAACzC,EAAE,CAAE;UAAAkC,QAAA,gBAEpCzC,OAAA;YAAMwC,SAAS,EAAC,UAAU;YAAAC,QAAA,EAAEO,GAAG,CAACT;UAAI;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,EAC3CG,GAAG,CAACV,KAAK;QAAA,GALLU,GAAG,CAACzC,EAAE;UAAAmC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAMP,CACX;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,eAEN7C,OAAA;QAAKwC,SAAS,EAAC,gBAAgB;QAAAC,QAAA,GAC1BtC,SAAS,KAAK,UAAU,iBACrBH,OAAA;UAAKwC,SAAS,EAAC,aAAa;UAAAC,QAAA,eACxBzC,OAAA;YAAKwC,SAAS,EAAC,cAAc;YAAAC,QAAA,gBACzBzC,OAAA;cAAIwC,SAAS,EAAC,YAAY;cAAAC,QAAA,EAAC;YAAwC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACxE7C,OAAA;cAAKwC,SAAS,EAAC,gBAAgB;cAAAC,QAAA,gBAC3BzC,OAAA;gBAAKwC,SAAS,EAAC,cAAc;gBAAAC,QAAA,gBACzBzC,OAAA;kBAAAyC,QAAA,EAAM;gBAAQ;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACrB7C,OAAA;kBAAAyC,QAAA,EAAM;gBAAM;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACnB7C,OAAA;kBAAAyC,QAAA,EAAM;gBAAQ;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACrB7C,OAAA;kBAAAyC,QAAA,EAAM;gBAAa;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eAC1B7C,OAAA;kBAAAyC,QAAA,EAAM;gBAAM;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACnB7C,OAAA;kBAAAyC,QAAA,EAAM;gBAAO;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnB,CAAC,EACLxC,gBAAgB,CAAC4B,MAAM,CAACN,GAAG,IAAIA,GAAG,CAACb,MAAM,KAAK,UAAU,CAAC,CAACY,GAAG,CAACwB,OAAO,iBAClElD,OAAA;gBAAsBwC,SAAS,EAAC,WAAW;gBAAAC,QAAA,gBACvCzC,OAAA;kBAAMwC,SAAS,EAAC,eAAe;kBAAAC,QAAA,EAAES,OAAO,CAAC1C;gBAAQ;kBAAAkC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACzD7C,OAAA;kBAAMwC,SAAS,EAAC,gBAAgB;kBAAAC,QAAA,GAAC,QAAC,EAACS,OAAO,CAACzC,MAAM;gBAAA;kBAAAiC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACzD7C,OAAA;kBAAMwC,SAAS,EAAC,kBAAkB;kBAAAC,QAAA,EAAES,OAAO,CAACxC;gBAAQ;kBAAAgC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAC5D7C,OAAA;kBAAMwC,SAAS,EAAC,cAAc;kBAAAC,QAAA,EAAES,OAAO,CAACrC;gBAAY;kBAAA6B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAC5D7C,OAAA;kBACIwC,SAAS,EAAC,gBAAgB;kBAC1BW,KAAK,EAAE;oBAAEC,KAAK,EAAErB,cAAc,CAACmB,OAAO,CAACpC,MAAM;kBAAE,CAAE;kBAAA2B,QAAA,EAEhDS,OAAO,CAACpC;gBAAM;kBAAA4B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACb,CAAC,eACP7C,OAAA;kBAAKwC,SAAS,EAAC,eAAe;kBAAAC,QAAA,gBAC1BzC,OAAA;oBACIwC,SAAS,EAAC,0BAA0B;oBACpCS,OAAO,EAAEA,CAAA,KAAM9B,kBAAkB,CAAC+B,OAAO,CAAE;oBAAAT,QAAA,EAC9C;kBAED;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,EACRK,OAAO,CAACnC,OAAO,iBACZf,OAAA;oBAAQwC,SAAS,EAAC,qBAAqB;oBAAAC,QAAA,EAAC;kBAAe;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAClE,eACD7C,OAAA;oBAAQwC,SAAS,EAAC,wBAAwB;oBAAAC,QAAA,EAAC;kBAAW;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC9D,CAAC;cAAA,GAtBAK,OAAO,CAAC3C,EAAE;gBAAAmC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAuBf,CACR,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CACR,EAEA1C,SAAS,KAAK,YAAY,iBACvBH,OAAA;UAAKwC,SAAS,EAAC,aAAa;UAAAC,QAAA,eACxBzC,OAAA;YAAKwC,SAAS,EAAC,cAAc;YAAAC,QAAA,gBACzBzC,OAAA;cAAIwC,SAAS,EAAC,YAAY;cAAAC,QAAA,EAAC;YAAmB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACnD7C,OAAA;cAAKwC,SAAS,EAAC,iBAAiB;cAAAC,QAAA,EAC3BpC,gBAAgB,CAAC4B,MAAM,CAACN,GAAG,IAAIA,GAAG,CAACb,MAAM,KAAK,YAAY,CAAC,CAACY,GAAG,CAACwB,OAAO,iBACpElD,OAAA;gBAAsBwC,SAAS,EAAC,iBAAiB;gBAAAC,QAAA,gBAC7CzC,OAAA;kBAAKwC,SAAS,EAAC,iBAAiB;kBAAAC,QAAA,gBAC5BzC,OAAA;oBAAKwC,SAAS,EAAC,mBAAmB;oBAAAC,QAAA,gBAC9BzC,OAAA;sBAAMwC,SAAS,EAAC,eAAe;sBAAAC,QAAA,EAAES,OAAO,CAAC1C;oBAAQ;sBAAAkC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC,eACzD7C,OAAA;sBAAMwC,SAAS,EAAC,mBAAmB;sBAAAC,QAAA,GAAC,QAAC,EAACS,OAAO,CAACzC,MAAM;oBAAA;sBAAAiC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC3D,CAAC,eACN7C,OAAA;oBAAKwC,SAAS,EAAC,oBAAoB;oBAAAC,QAAA,gBAC/BzC,OAAA;sBAAMwC,SAAS,EAAC,kBAAkB;sBAAAC,QAAA,EAAES,OAAO,CAACxC;oBAAQ;sBAAAgC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC,eAC5D7C,OAAA;sBAAMwC,SAAS,EAAC,iBAAiB;sBAAAC,QAAA,GAAC,cAAY,EAACS,OAAO,CAAClC,cAAc;oBAAA;sBAAA0B,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC5E,CAAC,eACN7C,OAAA;oBAAGwC,SAAS,EAAC,qBAAqB;oBAAAC,QAAA,EAAES,OAAO,CAACvC;kBAAW;oBAAA+B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC3D,CAAC,eACN7C,OAAA;kBAAKwC,SAAS,EAAC,oBAAoB;kBAAAC,QAAA,GAC9BS,OAAO,CAACjC,YAAY,iBACjBjB,OAAA;oBAAQwC,SAAS,EAAC,yBAAyB;oBAAAC,QAAA,EAAC;kBAAgB;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CACvE,EACAK,OAAO,CAACnC,OAAO,iBACZf,OAAA;oBAAQwC,SAAS,EAAC,qBAAqB;oBAAAC,QAAA,EAAC;kBAAU;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAC7D;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACA,CAAC;cAAA,GAnBAK,OAAO,CAAC3C,EAAE;gBAAAmC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAoBf,CACR;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CACR,EAEA1C,SAAS,KAAK,SAAS,iBACpBH,OAAA;UAAKwC,SAAS,EAAC,aAAa;UAAAC,QAAA,eACxBzC,OAAA;YAAKwC,SAAS,EAAC,cAAc;YAAAC,QAAA,gBACzBzC,OAAA;cAAIwC,SAAS,EAAC,YAAY;cAAAC,QAAA,EAAC;YAAgB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAChD7C,OAAA;cAAKwC,SAAS,EAAC,cAAc;cAAAC,QAAA,gBACzBzC,OAAA;gBAAKwC,SAAS,EAAC,aAAa;gBAAAC,QAAA,gBACxBzC,OAAA;kBAAKwC,SAAS,EAAC,aAAa;kBAAAC,QAAA,EAAC;gBAAE;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACrC7C,OAAA;kBAAIwC,SAAS,EAAC,cAAc;kBAAAC,QAAA,EAAC;gBAAc;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eAChD7C,OAAA;kBAAGwC,SAAS,EAAC,oBAAoB;kBAAAC,QAAA,EAAC;gBAElC;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC,eACJ7C,OAAA;kBACIwC,SAAS,EAAC,YAAY;kBACtBS,OAAO,EAAEA,CAAA,KAAMpB,cAAc,CAAC,SAAS,CAAE;kBAAAY,QAAA,EAC5C;gBAED;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACR,CAAC,eACN7C,OAAA;gBAAKwC,SAAS,EAAC,aAAa;gBAAAC,QAAA,gBACxBzC,OAAA;kBAAKwC,SAAS,EAAC,aAAa;kBAAAC,QAAA,EAAC;gBAAE;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACrC7C,OAAA;kBAAIwC,SAAS,EAAC,cAAc;kBAAAC,QAAA,EAAC;gBAAgB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eAClD7C,OAAA;kBAAGwC,SAAS,EAAC,oBAAoB;kBAAAC,QAAA,EAAC;gBAElC;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC,eACJ7C,OAAA;kBACIwC,SAAS,EAAC,YAAY;kBACtBS,OAAO,EAAEA,CAAA,KAAMpB,cAAc,CAAC,WAAW,CAAE;kBAAAY,QAAA,EAC9C;gBAED;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACR,CAAC,eACN7C,OAAA;gBAAKwC,SAAS,EAAC,aAAa;gBAAAC,QAAA,gBACxBzC,OAAA;kBAAKwC,SAAS,EAAC,aAAa;kBAAAC,QAAA,EAAC;gBAAE;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACrC7C,OAAA;kBAAIwC,SAAS,EAAC,cAAc;kBAAAC,QAAA,EAAC;gBAAe;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACjD7C,OAAA;kBAAGwC,SAAS,EAAC,oBAAoB;kBAAAC,QAAA,EAAC;gBAElC;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC,eACJ7C,OAAA;kBACIwC,SAAS,EAAC,YAAY;kBACtBS,OAAO,EAAEA,CAAA,KAAMpB,cAAc,CAAC,UAAU,CAAE;kBAAAY,QAAA,EAC7C;gBAED;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACR,CAAC,eACN7C,OAAA;gBAAKwC,SAAS,EAAC,aAAa;gBAAAC,QAAA,gBACxBzC,OAAA;kBAAKwC,SAAS,EAAC,aAAa;kBAAAC,QAAA,EAAC;gBAAG;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACtC7C,OAAA;kBAAIwC,SAAS,EAAC,cAAc;kBAAAC,QAAA,EAAC;gBAAe;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACjD7C,OAAA;kBAAGwC,SAAS,EAAC,oBAAoB;kBAAAC,QAAA,EAAC;gBAElC;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC,eACJ7C,OAAA;kBACIwC,SAAS,EAAC,YAAY;kBACtBS,OAAO,EAAEA,CAAA,KAAMpB,cAAc,CAAC,UAAU,CAAE;kBAAAY,QAAA,EAC7C;gBAED;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACR,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CACR,EAEA1C,SAAS,KAAK,UAAU,iBACrBH,OAAA;UAAKwC,SAAS,EAAC,aAAa;UAAAC,QAAA,eACxBzC,OAAA;YAAKwC,SAAS,EAAC,cAAc;YAAAC,QAAA,gBACzBzC,OAAA;cAAIwC,SAAS,EAAC,YAAY;cAAAC,QAAA,EAAC;YAAoB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACpD7C,OAAA;cAAKwC,SAAS,EAAC,eAAe;cAAAC,QAAA,EACzBpC,gBAAgB,CAAC4B,MAAM,CAACN,GAAG,IAAIA,GAAG,CAACZ,OAAO,CAAC,CAACW,GAAG,CAACwB,OAAO,iBACpDlD,OAAA;gBAAsBwC,SAAS,EAAC,cAAc;gBAAAC,QAAA,gBAC1CzC,OAAA;kBAAKwC,SAAS,EAAC,cAAc;kBAAAC,QAAA,gBACzBzC,OAAA;oBAAKwC,SAAS,EAAC,gBAAgB;oBAAAC,QAAA,gBAC3BzC,OAAA;sBAAMwC,SAAS,EAAC,eAAe;sBAAAC,QAAA,EAAES,OAAO,CAAC1C;oBAAQ;sBAAAkC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC,eACzD7C,OAAA;sBAAMwC,SAAS,EAAC,gBAAgB;sBAAAC,QAAA,GAAC,QAAC,EAACS,OAAO,CAACzC,MAAM;oBAAA;sBAAAiC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACxD,CAAC,eACN7C,OAAA;oBAAKwC,SAAS,EAAC,iBAAiB;oBAAAC,QAAA,gBAC5BzC,OAAA;sBAAMwC,SAAS,EAAC,kBAAkB;sBAAAC,QAAA,EAAES,OAAO,CAACxC;oBAAQ;sBAAAgC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC,eAC5D7C,OAAA;sBAAMwC,SAAS,EAAC,cAAc;sBAAAC,QAAA,EAAES,OAAO,CAACnC;oBAAO;sBAAA2B,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACtD,CAAC,eACN7C,OAAA;oBAAGwC,SAAS,EAAC,qBAAqB;oBAAAC,QAAA,EAAES,OAAO,CAACvC;kBAAW;oBAAA+B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC3D,CAAC,eACN7C,OAAA;kBAAKwC,SAAS,EAAC,iBAAiB;kBAAAC,QAAA,gBAC5BzC,OAAA;oBAAQwC,SAAS,EAAC,yBAAyB;oBAAAC,QAAA,EAAC;kBAAW;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eAChE7C,OAAA;oBAAQwC,SAAS,EAAC,qBAAqB;oBAAAC,QAAA,EAAC;kBAAQ;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eACzD7C,OAAA;oBAAQwC,SAAS,EAAC,uBAAuB;oBAAAC,QAAA,EAAC;kBAAQ;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1D,CAAC;cAAA,GAhBAK,OAAO,CAAC3C,EAAE;gBAAAmC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAiBf,CACR;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CACR;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC,EAGL3B,eAAe,iBACZlB,OAAA;MAAKwC,SAAS,EAAC,eAAe;MAAAC,QAAA,eAC1BzC,OAAA;QAAKwC,SAAS,EAAC,eAAe;QAAAC,QAAA,gBAC1BzC,OAAA;UAAAyC,QAAA,EAAI;QAAkB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC3B7C,OAAA;UAAAyC,QAAA,GAAG,+BAA6B,EAACvB,eAAe,CAACV,QAAQ,EAAC,WAAI,EAACU,eAAe,CAACT,MAAM;QAAA;UAAAiC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAE1F7C,OAAA;UAAKwC,SAAS,EAAC,YAAY;UAAAC,QAAA,gBACvBzC,OAAA;YAAOwC,SAAS,EAAC,YAAY;YAAAC,QAAA,EAAC;UAAkB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACxD7C,OAAA;YACI8B,IAAI,EAAC,MAAM;YACXU,SAAS,EAAC,YAAY;YACtBa,KAAK,EAAEhC,iBAAkB;YACzBiC,QAAQ,EAAGC,CAAC,IAAKjC,oBAAoB,CAACiC,CAAC,CAACC,MAAM,CAACH,KAAK;UAAE;YAAAX,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,eAEN7C,OAAA;UAAKwC,SAAS,EAAC,YAAY;UAAAC,QAAA,gBACvBzC,OAAA;YAAOwC,SAAS,EAAC,YAAY;YAAAC,QAAA,EAAC;UAAwB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eAC9D7C,OAAA;YACI8B,IAAI,EAAC,MAAM;YACXU,SAAS,EAAC,uBAAuB;YACjCiB,MAAM,EAAC,sBAAsB;YAC7BH,QAAQ,EAAGC,CAAC,IAAKnC,eAAe,CAACmC,CAAC,CAACC,MAAM,CAACE,KAAK,CAAC,CAAC,CAAC;UAAE;YAAAhB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvD,CAAC,eACF7C,OAAA;YAAOwC,SAAS,EAAC,WAAW;YAAAC,QAAA,EAAC;UAAoD;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxF,CAAC,eAEN7C,OAAA;UAAKwC,SAAS,EAAC,eAAe;UAAAC,QAAA,gBAC1BzC,OAAA;YACIwC,SAAS,EAAC,0BAA0B;YACpCS,OAAO,EAAEA,CAAA,KAAM1B,oBAAoB,CAACL,eAAe,CAACX,EAAE,CAAE;YAAAkC,QAAA,EAC3D;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACT7C,OAAA;YACIwC,SAAS,EAAC,uBAAuB;YACjCS,OAAO,EAAEA,CAAA,KAAM9B,kBAAkB,CAAC,IAAI,CAAE;YAAAsB,QAAA,EAC3C;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CACR;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACA,CAAC;AAEd;AAAC3C,EAAA,CAhXQD,gBAAgB;AAAA0D,EAAA,GAAhB1D,gBAAgB;AAkXzB,eAAeA,gBAAgB;AAAC,IAAA0D,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}