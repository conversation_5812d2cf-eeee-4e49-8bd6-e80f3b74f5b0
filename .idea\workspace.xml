<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="20629654-a2e9-4c16-8547-9d8fb21b54a5" name="Changes" comment="">
      <change afterPath="$PROJECT_DIR$/expensetracker/.env" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/expensetracker/src/components/home/<USER>" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/expensetracker/src/components/home/<USER>" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/expensetracker/src/components/home/<USER>" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/expensetracker/src/components/home/<USER>" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/expensetracker/src/components/home/<USER>" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/expensetracker/src/pages/Contact.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/expensetracker/package-lock.json" beforeDir="false" afterPath="$PROJECT_DIR$/expensetracker/package-lock.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/expensetracker/package.json" beforeDir="false" afterPath="$PROJECT_DIR$/expensetracker/package.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/expensetracker/src/App.css" beforeDir="false" afterPath="$PROJECT_DIR$/expensetracker/src/App.css" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/expensetracker/src/App.js" beforeDir="false" afterPath="$PROJECT_DIR$/expensetracker/src/App.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/expensetracker/src/App.test.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/expensetracker/src/index.css" beforeDir="false" afterPath="$PROJECT_DIR$/expensetracker/src/index.css" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/expensetracker/src/index.js" beforeDir="false" afterPath="$PROJECT_DIR$/expensetracker/src/index.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/expensetracker/src/logo.svg" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/expensetracker/src/reportWebVitals.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/expensetracker/src/setupTests.js" beforeDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$/expensetracker" />
  </component>
  <component name="ProblemsViewState">
    <option name="selectedTabId" value="CurrentFile" />
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 2
}</component>
  <component name="ProjectId" id="30QQ9Pnw7f3DdU9AoBr39lrKy1J" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent"><![CDATA[{
  "keyToString": {
    "ModuleVcsDetector.initialDetectionPerformed": "true",
    "RunOnceActivity.ShowReadmeOnStart": "true",
    "RunOnceActivity.TerminalTabsStorage.copyFrom.TerminalArrangementManager": "true",
    "RunOnceActivity.git.unshallow": "true",
    "git-widget-placeholder": "master",
    "ignore.virus.scanning.warn.message": "true",
    "junie.onboarding.icon.badge.shown": "true",
    "last_opened_file_path": "D:/Expense Tracker App (MERN )",
    "node.js.detected.package.eslint": "true",
    "node.js.detected.package.tslint": "true",
    "node.js.selected.package.eslint": "(autodetect)",
    "node.js.selected.package.tslint": "(autodetect)",
    "nodejs_package_manager_path": "npm",
    "settings.editor.selected.configurable": "preferences.pluginManager",
    "vue.rearranger.settings.migration": "true"
  }
}]]></component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-js-predefined-d6986cc7102b-09060db00ec0-JavaScript-WS-251.27812.50" />
      </set>
    </attachedChunks>
  </component>
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="20629654-a2e9-4c16-8547-9d8fb21b54a5" name="Changes" comment="" />
      <created>1753557168303</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1753557168303</updated>
      <workItem from="1753557168306" duration="1368000" />
      <workItem from="1753563222802" duration="7010000" />
    </task>
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
</project>