{"ast": null, "code": "var _jsxFileName = \"D:\\\\Expense Tracker App (MERN )\\\\expensetracker\\\\src\\\\components\\\\Loader.js\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction Loader() {\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"custom-loader\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"d-flex justify-content-center align-items-center gap-3\",\n      style: {\n        height: \"100vh\"\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"spinner-border text-primary\",\n        role: \"status\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 5,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n        className: \"text-light fs-5\",\n        children: \"Loading...\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 6,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 4,\n      columnNumber: 13\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 3,\n    columnNumber: 9\n  }, this);\n}\n_c = Loader;\nexport default Loader;\nvar _c;\n$RefreshReg$(_c, \"Loader\");", "map": {"version": 3, "names": ["Loader", "_jsxDEV", "className", "children", "style", "height", "role", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["D:/Expense Tracker App (MERN )/expensetracker/src/components/Loader.js"], "sourcesContent": ["function Loader() {\r\n    return (\r\n        <div className=\"custom-loader\">\r\n            <div className=\"d-flex justify-content-center align-items-center gap-3\" style={{ height: \"100vh\" }}>\r\n                <div className=\"spinner-border text-primary\" role=\"status\"></div>\r\n                <span className=\"text-light fs-5\">Loading...</span>\r\n            </div>\r\n        </div>\r\n    );\r\n}\r\n\r\nexport default Loader;\r\n"], "mappings": ";;AAAA,SAASA,MAAMA,CAAA,EAAG;EACd,oBACIC,OAAA;IAAKC,SAAS,EAAC,eAAe;IAAAC,QAAA,eAC1BF,OAAA;MAAKC,SAAS,EAAC,wDAAwD;MAACE,KAAK,EAAE;QAAEC,MAAM,EAAE;MAAQ,CAAE;MAAAF,QAAA,gBAC/FF,OAAA;QAAKC,SAAS,EAAC,6BAA6B;QAACI,IAAI,EAAC;MAAQ;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eACjET,OAAA;QAAMC,SAAS,EAAC,iBAAiB;QAAAC,QAAA,EAAC;MAAU;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAClD;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;AAEd;AAACC,EAAA,GATQX,MAAM;AAWf,eAAeA,MAAM;AAAC,IAAAW,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}