{"ast": null, "code": "var _jsxFileName = \"D:\\\\Expense Tracker App (MERN )\\\\expensetracker\\\\src\\\\components\\\\home\\\\Hero.js\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction Hero() {\n  return /*#__PURE__*/_jsxDEV(\"section\", {\n    className: \"container text-center py-5\",\n    children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n      className: \"display-4 text-primary\",\n      children: \"Track Every Rupee\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 4,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n      className: \"lead\",\n      children: \"Smart. Simple. Secure.\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 5,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n      href: \"/signup\",\n      className: \"btn btn-outline-primary btn-lg mt-3\",\n      children: \"Get Started\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 6,\n      columnNumber: 13\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 3,\n    columnNumber: 9\n  }, this);\n}\n_c = Hero;\nexport default Hero;\nvar _c;\n$RefreshReg$(_c, \"Hero\");", "map": {"version": 3, "names": ["Hero", "_jsxDEV", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "href", "_c", "$RefreshReg$"], "sources": ["D:/Expense Tracker App (MERN )/expensetracker/src/components/home/<USER>"], "sourcesContent": ["function Hero() {\r\n    return (\r\n        <section className=\"container text-center py-5\">\r\n            <h1 className=\"display-4 text-primary\">Track Every Rupee</h1>\r\n            <p className=\"lead\">Smart. Simple. Secure.</p>\r\n            <a href=\"/signup\" className=\"btn btn-outline-primary btn-lg mt-3\">Get Started</a>\r\n        </section>\r\n    );\r\n}\r\nexport default Hero;"], "mappings": ";;AAAA,SAASA,IAAIA,CAAA,EAAG;EACZ,oBACIC,OAAA;IAASC,SAAS,EAAC,4BAA4B;IAAAC,QAAA,gBAC3CF,OAAA;MAAIC,SAAS,EAAC,wBAAwB;MAAAC,QAAA,EAAC;IAAiB;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,eAC7DN,OAAA;MAAGC,SAAS,EAAC,MAAM;MAAAC,QAAA,EAAC;IAAsB;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAG,CAAC,eAC9CN,OAAA;MAAGO,IAAI,EAAC,SAAS;MAACN,SAAS,EAAC,qCAAqC;MAAAC,QAAA,EAAC;IAAW;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAG,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAC5E,CAAC;AAElB;AAACE,EAAA,GARQT,IAAI;AASb,eAAeA,IAAI;AAAC,IAAAS,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}