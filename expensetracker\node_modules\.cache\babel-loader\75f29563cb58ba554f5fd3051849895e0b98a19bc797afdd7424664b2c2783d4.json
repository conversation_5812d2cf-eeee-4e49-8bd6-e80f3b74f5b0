{"ast": null, "code": "var _jsxFileName = \"D:\\\\Expense Tracker App (MERN )\\\\expensetracker\\\\src\\\\pages\\\\AdminDashboard.js\",\n  _s = $RefreshSig$();\nimport { useState } from 'react';\nimport './Dashboard.css';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nfunction AdminDashboard() {\n  _s();\n  const [activeTab, setActiveTab] = useState('expenses');\n  const [expenses, setExpenses] = useState([{\n    id: 1,\n    employee: '<PERSON>',\n    amount: 1200,\n    category: 'Travel',\n    description: 'Flight tickets for business trip',\n    date: '2024-01-15',\n    status: 'Pending',\n    receipt: 'receipt_001.pdf'\n  }, {\n    id: 2,\n    employee: '<PERSON>',\n    amount: 850,\n    category: 'Meals',\n    description: 'Client dinner meeting',\n    date: '2024-01-14',\n    status: 'Approved',\n    receipt: null\n  }, {\n    id: 3,\n    employee: '<PERSON>',\n    amount: 2500,\n    category: 'Equipment',\n    description: 'New laptop for development',\n    date: '2024-01-12',\n    status: 'Pending',\n    receipt: 'receipt_003.pdf'\n  }]);\n  const [users] = useState([{\n    id: 1,\n    name: '<PERSON>e',\n    email: '<EMAIL>',\n    role: 'Employee',\n    department: 'Sales'\n  }, {\n    id: 2,\n    name: 'Jane Smith',\n    email: '<EMAIL>',\n    role: 'Employee',\n    department: 'Marketing'\n  }, {\n    id: 3,\n    name: '<PERSON> Johnson',\n    email: '<EMAIL>',\n    role: 'Employee',\n    department: 'Development'\n  }, {\n    id: 4,\n    name: 'Sarah Wilson',\n    email: '<EMAIL>',\n    role: 'Finance',\n    department: 'Finance'\n  }]);\n  const [categories, setCategories] = useState([{\n    id: 1,\n    name: 'Travel',\n    active: true,\n    count: 15\n  }, {\n    id: 2,\n    name: 'Meals',\n    active: true,\n    count: 8\n  }, {\n    id: 3,\n    name: 'Equipment',\n    active: true,\n    count: 12\n  }, {\n    id: 4,\n    name: 'Office Supplies',\n    active: true,\n    count: 5\n  }, {\n    id: 5,\n    name: 'Training',\n    active: false,\n    count: 2\n  }]);\n  const [filterStatus, setFilterStatus] = useState('All');\n  const [selectedExpense, setSelectedExpense] = useState(null);\n  const [rejectReason, setRejectReason] = useState('');\n  const handleApprove = expenseId => {\n    setExpenses(expenses.map(exp => exp.id === expenseId ? {\n      ...exp,\n      status: 'Approved'\n    } : exp));\n    alert('Expense approved successfully!');\n  };\n  const handleReject = expenseId => {\n    if (!rejectReason.trim()) {\n      alert('Please provide a reason for rejection');\n      return;\n    }\n    setExpenses(expenses.map(exp => exp.id === expenseId ? {\n      ...exp,\n      status: 'Rejected',\n      rejectReason\n    } : exp));\n    setSelectedExpense(null);\n    setRejectReason('');\n    alert('Expense rejected successfully!');\n  };\n  const getStatusColor = status => {\n    switch (status) {\n      case 'Approved':\n        return 'var(--accent-tertiary)';\n      case 'Rejected':\n        return 'var(--accent-secondary)';\n      case 'Pending':\n        return 'var(--text-secondary)';\n      default:\n        return 'var(--text-secondary)';\n    }\n  };\n  const filteredExpenses = filterStatus === 'All' ? expenses : expenses.filter(exp => exp.status === filterStatus);\n  const tabs = [{\n    id: 'expenses',\n    label: 'All Expenses',\n    icon: '📋'\n  }, {\n    id: 'users',\n    label: 'User Management',\n    icon: '👥'\n  }, {\n    id: 'categories',\n    label: 'Categories',\n    icon: '🏷️'\n  }, {\n    id: 'audit',\n    label: 'Audit Trail',\n    icon: '🔍'\n  }];\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"dashboard-container\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"dashboard-background\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"dashboard-particles\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 101,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 100,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"dashboard-content\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"dashboard-header\",\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          className: \"dashboard-title\",\n          children: [\"Admin \", /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"gradient-text\",\n            children: \"Dashboard\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 107,\n            columnNumber: 31\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 106,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"dashboard-subtitle\",\n          children: \"Manage expenses, users, and system settings\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 109,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 105,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"dashboard-stats\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"stat-card\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"stat-icon\",\n            children: \"\\uD83D\\uDCCA\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 114,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"stat-info\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"stat-value\",\n              children: \"24\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 116,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"stat-label\",\n              children: \"Total Expenses\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 117,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 115,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 113,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"stat-card\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"stat-icon\",\n            children: \"\\u23F3\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 121,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"stat-info\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"stat-value\",\n              children: \"8\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 123,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"stat-label\",\n              children: \"Pending Review\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 124,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 122,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 120,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"stat-card\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"stat-icon\",\n            children: \"\\u2705\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 128,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"stat-info\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"stat-value\",\n              children: \"12\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 130,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"stat-label\",\n              children: \"Approved\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 131,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 129,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 127,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"stat-card\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"stat-icon\",\n            children: \"\\uD83D\\uDCB0\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 135,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"stat-info\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"stat-value\",\n              children: \"\\u20B945,200\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 137,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"stat-label\",\n              children: \"Total Amount\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 138,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 136,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 134,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 112,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"dashboard-tabs\",\n        children: tabs.map(tab => /*#__PURE__*/_jsxDEV(\"button\", {\n          className: `tab-btn ${activeTab === tab.id ? 'active' : ''}`,\n          onClick: () => setActiveTab(tab.id),\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"tab-icon\",\n            children: tab.icon\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 150,\n            columnNumber: 29\n          }, this), tab.label]\n        }, tab.id, true, {\n          fileName: _jsxFileName,\n          lineNumber: 145,\n          columnNumber: 25\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 143,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"dashboard-main\",\n        children: [activeTab === 'expenses' && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"tab-content\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"content-card\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"card-header\",\n              children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n                className: \"card-title\",\n                children: \"All Submitted Expenses\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 161,\n                columnNumber: 37\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"filter-controls\",\n                children: /*#__PURE__*/_jsxDEV(\"select\", {\n                  className: \"filter-select\",\n                  value: filterStatus,\n                  onChange: e => setFilterStatus(e.target.value),\n                  children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"All\",\n                    children: \"All Status\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 168,\n                    columnNumber: 45\n                  }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"Pending\",\n                    children: \"Pending\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 169,\n                    columnNumber: 45\n                  }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"Approved\",\n                    children: \"Approved\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 170,\n                    columnNumber: 45\n                  }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"Rejected\",\n                    children: \"Rejected\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 171,\n                    columnNumber: 45\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 163,\n                  columnNumber: 41\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 162,\n                columnNumber: 37\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 160,\n              columnNumber: 33\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"expenses-table\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"table-header\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  children: \"Employee\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 177,\n                  columnNumber: 41\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  children: \"Amount\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 178,\n                  columnNumber: 41\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  children: \"Category\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 179,\n                  columnNumber: 41\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  children: \"Date\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 180,\n                  columnNumber: 41\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  children: \"Status\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 181,\n                  columnNumber: 41\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  children: \"Actions\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 182,\n                  columnNumber: 41\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 176,\n                columnNumber: 37\n              }, this), filteredExpenses.map(expense => /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"table-row\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"employee-name\",\n                  children: expense.employee\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 186,\n                  columnNumber: 45\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"expense-amount\",\n                  children: [\"\\u20B9\", expense.amount]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 187,\n                  columnNumber: 45\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"expense-category\",\n                  children: expense.category\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 188,\n                  columnNumber: 45\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"expense-date\",\n                  children: expense.date\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 189,\n                  columnNumber: 45\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"expense-status\",\n                  style: {\n                    color: getStatusColor(expense.status)\n                  },\n                  children: expense.status\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 190,\n                  columnNumber: 45\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"table-actions\",\n                  children: [expense.status === 'Pending' && /*#__PURE__*/_jsxDEV(_Fragment, {\n                    children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                      className: \"action-btn approve-btn\",\n                      onClick: () => handleApprove(expense.id),\n                      children: \"\\u2705 Approve\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 199,\n                      columnNumber: 57\n                    }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                      className: \"action-btn reject-btn\",\n                      onClick: () => setSelectedExpense(expense),\n                      children: \"\\u274C Reject\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 205,\n                      columnNumber: 57\n                    }, this)]\n                  }, void 0, true), expense.receipt && /*#__PURE__*/_jsxDEV(\"button\", {\n                    className: \"action-btn view-btn\",\n                    children: \"\\uD83D\\uDCC4 View Receipt\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 214,\n                    columnNumber: 53\n                  }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                    className: \"action-btn details-btn\",\n                    children: \"\\uD83D\\uDC41\\uFE0F Details\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 216,\n                    columnNumber: 49\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 196,\n                  columnNumber: 45\n                }, this)]\n              }, expense.id, true, {\n                fileName: _jsxFileName,\n                lineNumber: 185,\n                columnNumber: 41\n              }, this))]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 175,\n              columnNumber: 33\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 159,\n            columnNumber: 29\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 158,\n          columnNumber: 25\n        }, this), activeTab === 'users' && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"tab-content\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"content-card\",\n            children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n              className: \"card-title\",\n              children: \"User Management\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 228,\n              columnNumber: 33\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"users-grid\",\n              children: users.map(user => /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"user-card\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"user-avatar\",\n                  children: \"\\uD83D\\uDC64\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 232,\n                  columnNumber: 45\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"user-info\",\n                  children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                    className: \"user-name\",\n                    children: user.name\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 234,\n                    columnNumber: 49\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"user-email\",\n                    children: user.email\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 235,\n                    columnNumber: 49\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"user-meta\",\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"user-role\",\n                      children: user.role\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 237,\n                      columnNumber: 53\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"user-department\",\n                      children: user.department\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 238,\n                      columnNumber: 53\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 236,\n                    columnNumber: 49\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 233,\n                  columnNumber: 45\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"user-actions\",\n                  children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                    className: \"action-btn view-btn\",\n                    children: \"\\uD83D\\uDC41\\uFE0F View Profile\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 242,\n                    columnNumber: 49\n                  }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                    className: \"action-btn details-btn\",\n                    children: \"\\uD83D\\uDCCA Expenses\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 243,\n                    columnNumber: 49\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 241,\n                  columnNumber: 45\n                }, this)]\n              }, user.id, true, {\n                fileName: _jsxFileName,\n                lineNumber: 231,\n                columnNumber: 41\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 229,\n              columnNumber: 33\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 227,\n            columnNumber: 29\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 226,\n          columnNumber: 25\n        }, this), activeTab === 'categories' && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"tab-content\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"content-card\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"card-header\",\n              children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n                className: \"card-title\",\n                children: \"Expense Categories\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 256,\n                columnNumber: 37\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                className: \"add-btn\",\n                children: \"\\u2795 Add Category\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 257,\n                columnNumber: 37\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 255,\n              columnNumber: 33\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"categories-list\",\n              children: categories.map(category => /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"category-item\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"category-info\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"category-name\",\n                    children: category.name\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 263,\n                    columnNumber: 49\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"category-count\",\n                    children: [category.count, \" expenses\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 264,\n                    columnNumber: 49\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 262,\n                  columnNumber: 45\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"category-status\",\n                  children: /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: `status-badge ${category.active ? 'active' : 'inactive'}`,\n                    children: category.active ? 'Active' : 'Inactive'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 267,\n                    columnNumber: 49\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 266,\n                  columnNumber: 45\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"category-actions\",\n                  children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                    className: \"action-btn edit-btn\",\n                    children: \"\\u270F\\uFE0F Edit\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 272,\n                    columnNumber: 49\n                  }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                    className: \"action-btn toggle-btn\",\n                    children: category.active ? '🔒 Deactivate' : '🔓 Activate'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 273,\n                    columnNumber: 49\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 271,\n                  columnNumber: 45\n                }, this)]\n              }, category.id, true, {\n                fileName: _jsxFileName,\n                lineNumber: 261,\n                columnNumber: 41\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 259,\n              columnNumber: 33\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 254,\n            columnNumber: 29\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 253,\n          columnNumber: 25\n        }, this), activeTab === 'audit' && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"tab-content\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"content-card\",\n            children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n              className: \"card-title\",\n              children: \"Audit Trail\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 287,\n              columnNumber: 33\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"audit-list\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"audit-item\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"audit-icon\",\n                  children: \"\\u2705\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 290,\n                  columnNumber: 41\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"audit-info\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"audit-action\",\n                    children: \"Expense Approved\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 292,\n                    columnNumber: 45\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"audit-details\",\n                    children: \"John Doe's travel expense (\\u20B91,200) approved\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 293,\n                    columnNumber: 45\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"audit-time\",\n                    children: \"2 hours ago\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 294,\n                    columnNumber: 45\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 291,\n                  columnNumber: 41\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 289,\n                columnNumber: 37\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"audit-item\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"audit-icon\",\n                  children: \"\\u274C\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 298,\n                  columnNumber: 41\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"audit-info\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"audit-action\",\n                    children: \"Expense Rejected\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 300,\n                    columnNumber: 45\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"audit-details\",\n                    children: \"Jane Smith's meal expense (\\u20B9850) rejected - Missing receipt\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 301,\n                    columnNumber: 45\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"audit-time\",\n                    children: \"4 hours ago\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 302,\n                    columnNumber: 45\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 299,\n                  columnNumber: 41\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 297,\n                columnNumber: 37\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"audit-item\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"audit-icon\",\n                  children: \"\\uD83D\\uDCE4\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 306,\n                  columnNumber: 41\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"audit-info\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"audit-action\",\n                    children: \"New Expense Submitted\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 308,\n                    columnNumber: 45\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"audit-details\",\n                    children: \"Mike Johnson submitted equipment expense (\\u20B92,500)\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 309,\n                    columnNumber: 45\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"audit-time\",\n                    children: \"6 hours ago\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 310,\n                    columnNumber: 45\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 307,\n                  columnNumber: 41\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 305,\n                columnNumber: 37\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 288,\n              columnNumber: 33\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 286,\n            columnNumber: 29\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 285,\n          columnNumber: 25\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 156,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 104,\n      columnNumber: 13\n    }, this), selectedExpense && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"modal-overlay\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"modal-content\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          children: \"Reject Expense\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 324,\n          columnNumber: 25\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: [\"Rejecting expense from \", selectedExpense.employee, \" for \\u20B9\", selectedExpense.amount]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 325,\n          columnNumber: 25\n        }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n          className: \"form-input\",\n          placeholder: \"Reason for rejection...\",\n          value: rejectReason,\n          onChange: e => setRejectReason(e.target.value),\n          rows: \"3\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 326,\n          columnNumber: 25\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"modal-actions\",\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"action-btn reject-btn\",\n            onClick: () => handleReject(selectedExpense.id),\n            children: \"Reject Expense\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 334,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"action-btn cancel-btn\",\n            onClick: () => setSelectedExpense(null),\n            children: \"Cancel\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 340,\n            columnNumber: 29\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 333,\n          columnNumber: 25\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 323,\n        columnNumber: 21\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 322,\n      columnNumber: 17\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 99,\n    columnNumber: 9\n  }, this);\n}\n_s(AdminDashboard, \"QfND5Ddpzl8pSj7nrBcgT5KH7wo=\");\n_c = AdminDashboard;\nexport default AdminDashboard;\nvar _c;\n$RefreshReg$(_c, \"AdminDashboard\");", "map": {"version": 3, "names": ["useState", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "AdminDashboard", "_s", "activeTab", "setActiveTab", "expenses", "setExpenses", "id", "employee", "amount", "category", "description", "date", "status", "receipt", "users", "name", "email", "role", "department", "categories", "setCategories", "active", "count", "filterStatus", "setFilterStatus", "selectedExpense", "setSelectedExpense", "rejectReason", "setRejectReason", "handleApprove", "expenseId", "map", "exp", "alert", "handleReject", "trim", "getStatusColor", "filteredExpenses", "filter", "tabs", "label", "icon", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "tab", "onClick", "value", "onChange", "e", "target", "expense", "style", "color", "user", "placeholder", "rows", "_c", "$RefreshReg$"], "sources": ["D:/Expense Tracker App (MERN )/expensetracker/src/pages/AdminDashboard.js"], "sourcesContent": ["import { useState } from 'react';\nimport './Dashboard.css';\n\nfunction AdminDashboard() {\n    const [activeTab, setActiveTab] = useState('expenses');\n    const [expenses, setExpenses] = useState([\n        {\n            id: 1,\n            employee: '<PERSON>',\n            amount: 1200,\n            category: 'Travel',\n            description: 'Flight tickets for business trip',\n            date: '2024-01-15',\n            status: 'Pending',\n            receipt: 'receipt_001.pdf'\n        },\n        {\n            id: 2,\n            employee: '<PERSON>',\n            amount: 850,\n            category: 'Meals',\n            description: 'Client dinner meeting',\n            date: '2024-01-14',\n            status: 'Approved',\n            receipt: null\n        },\n        {\n            id: 3,\n            employee: '<PERSON>',\n            amount: 2500,\n            category: 'Equipment',\n            description: 'New laptop for development',\n            date: '2024-01-12',\n            status: 'Pending',\n            receipt: 'receipt_003.pdf'\n        }\n    ]);\n\n    const [users] = useState([\n        { id: 1, name: '<PERSON>', email: '<EMAIL>', role: 'Employee', department: 'Sales' },\n        { id: 2, name: '<PERSON>', email: '<EMAIL>', role: 'Employee', department: 'Marketing' },\n        { id: 3, name: '<PERSON>', email: '<EMAIL>', role: 'Employee', department: 'Development' },\n        { id: 4, name: '<PERSON> <PERSON>', email: '<EMAIL>', role: 'Finance', department: 'Finance' }\n    ]);\n\n    const [categories, setCategories] = useState([\n        { id: 1, name: 'Travel', active: true, count: 15 },\n        { id: 2, name: 'Meals', active: true, count: 8 },\n        { id: 3, name: 'Equipment', active: true, count: 12 },\n        { id: 4, name: 'Office Supplies', active: true, count: 5 },\n        { id: 5, name: 'Training', active: false, count: 2 }\n    ]);\n\n    const [filterStatus, setFilterStatus] = useState('All');\n    const [selectedExpense, setSelectedExpense] = useState(null);\n    const [rejectReason, setRejectReason] = useState('');\n\n    const handleApprove = (expenseId) => {\n        setExpenses(expenses.map(exp => \n            exp.id === expenseId ? { ...exp, status: 'Approved' } : exp\n        ));\n        alert('Expense approved successfully!');\n    };\n\n    const handleReject = (expenseId) => {\n        if (!rejectReason.trim()) {\n            alert('Please provide a reason for rejection');\n            return;\n        }\n        setExpenses(expenses.map(exp => \n            exp.id === expenseId ? { ...exp, status: 'Rejected', rejectReason } : exp\n        ));\n        setSelectedExpense(null);\n        setRejectReason('');\n        alert('Expense rejected successfully!');\n    };\n\n    const getStatusColor = (status) => {\n        switch (status) {\n            case 'Approved': return 'var(--accent-tertiary)';\n            case 'Rejected': return 'var(--accent-secondary)';\n            case 'Pending': return 'var(--text-secondary)';\n            default: return 'var(--text-secondary)';\n        }\n    };\n\n    const filteredExpenses = filterStatus === 'All' \n        ? expenses \n        : expenses.filter(exp => exp.status === filterStatus);\n\n    const tabs = [\n        { id: 'expenses', label: 'All Expenses', icon: '📋' },\n        { id: 'users', label: 'User Management', icon: '👥' },\n        { id: 'categories', label: 'Categories', icon: '🏷️' },\n        { id: 'audit', label: 'Audit Trail', icon: '🔍' }\n    ];\n\n    return (\n        <div className=\"dashboard-container\">\n            <div className=\"dashboard-background\">\n                <div className=\"dashboard-particles\"></div>\n            </div>\n\n            <div className=\"dashboard-content\">\n                <div className=\"dashboard-header\">\n                    <h1 className=\"dashboard-title\">\n                        Admin <span className=\"gradient-text\">Dashboard</span>\n                    </h1>\n                    <p className=\"dashboard-subtitle\">Manage expenses, users, and system settings</p>\n                </div>\n\n                <div className=\"dashboard-stats\">\n                    <div className=\"stat-card\">\n                        <div className=\"stat-icon\">📊</div>\n                        <div className=\"stat-info\">\n                            <span className=\"stat-value\">24</span>\n                            <span className=\"stat-label\">Total Expenses</span>\n                        </div>\n                    </div>\n                    <div className=\"stat-card\">\n                        <div className=\"stat-icon\">⏳</div>\n                        <div className=\"stat-info\">\n                            <span className=\"stat-value\">8</span>\n                            <span className=\"stat-label\">Pending Review</span>\n                        </div>\n                    </div>\n                    <div className=\"stat-card\">\n                        <div className=\"stat-icon\">✅</div>\n                        <div className=\"stat-info\">\n                            <span className=\"stat-value\">12</span>\n                            <span className=\"stat-label\">Approved</span>\n                        </div>\n                    </div>\n                    <div className=\"stat-card\">\n                        <div className=\"stat-icon\">💰</div>\n                        <div className=\"stat-info\">\n                            <span className=\"stat-value\">₹45,200</span>\n                            <span className=\"stat-label\">Total Amount</span>\n                        </div>\n                    </div>\n                </div>\n\n                <div className=\"dashboard-tabs\">\n                    {tabs.map(tab => (\n                        <button\n                            key={tab.id}\n                            className={`tab-btn ${activeTab === tab.id ? 'active' : ''}`}\n                            onClick={() => setActiveTab(tab.id)}\n                        >\n                            <span className=\"tab-icon\">{tab.icon}</span>\n                            {tab.label}\n                        </button>\n                    ))}\n                </div>\n\n                <div className=\"dashboard-main\">\n                    {activeTab === 'expenses' && (\n                        <div className=\"tab-content\">\n                            <div className=\"content-card\">\n                                <div className=\"card-header\">\n                                    <h2 className=\"card-title\">All Submitted Expenses</h2>\n                                    <div className=\"filter-controls\">\n                                        <select \n                                            className=\"filter-select\"\n                                            value={filterStatus}\n                                            onChange={(e) => setFilterStatus(e.target.value)}\n                                        >\n                                            <option value=\"All\">All Status</option>\n                                            <option value=\"Pending\">Pending</option>\n                                            <option value=\"Approved\">Approved</option>\n                                            <option value=\"Rejected\">Rejected</option>\n                                        </select>\n                                    </div>\n                                </div>\n                                <div className=\"expenses-table\">\n                                    <div className=\"table-header\">\n                                        <span>Employee</span>\n                                        <span>Amount</span>\n                                        <span>Category</span>\n                                        <span>Date</span>\n                                        <span>Status</span>\n                                        <span>Actions</span>\n                                    </div>\n                                    {filteredExpenses.map(expense => (\n                                        <div key={expense.id} className=\"table-row\">\n                                            <span className=\"employee-name\">{expense.employee}</span>\n                                            <span className=\"expense-amount\">₹{expense.amount}</span>\n                                            <span className=\"expense-category\">{expense.category}</span>\n                                            <span className=\"expense-date\">{expense.date}</span>\n                                            <span \n                                                className=\"expense-status\"\n                                                style={{ color: getStatusColor(expense.status) }}\n                                            >\n                                                {expense.status}\n                                            </span>\n                                            <div className=\"table-actions\">\n                                                {expense.status === 'Pending' && (\n                                                    <>\n                                                        <button \n                                                            className=\"action-btn approve-btn\"\n                                                            onClick={() => handleApprove(expense.id)}\n                                                        >\n                                                            ✅ Approve\n                                                        </button>\n                                                        <button \n                                                            className=\"action-btn reject-btn\"\n                                                            onClick={() => setSelectedExpense(expense)}\n                                                        >\n                                                            ❌ Reject\n                                                        </button>\n                                                    </>\n                                                )}\n                                                {expense.receipt && (\n                                                    <button className=\"action-btn view-btn\">📄 View Receipt</button>\n                                                )}\n                                                <button className=\"action-btn details-btn\">👁️ Details</button>\n                                            </div>\n                                        </div>\n                                    ))}\n                                </div>\n                            </div>\n                        </div>\n                    )}\n\n                    {activeTab === 'users' && (\n                        <div className=\"tab-content\">\n                            <div className=\"content-card\">\n                                <h2 className=\"card-title\">User Management</h2>\n                                <div className=\"users-grid\">\n                                    {users.map(user => (\n                                        <div key={user.id} className=\"user-card\">\n                                            <div className=\"user-avatar\">👤</div>\n                                            <div className=\"user-info\">\n                                                <h3 className=\"user-name\">{user.name}</h3>\n                                                <p className=\"user-email\">{user.email}</p>\n                                                <div className=\"user-meta\">\n                                                    <span className=\"user-role\">{user.role}</span>\n                                                    <span className=\"user-department\">{user.department}</span>\n                                                </div>\n                                            </div>\n                                            <div className=\"user-actions\">\n                                                <button className=\"action-btn view-btn\">👁️ View Profile</button>\n                                                <button className=\"action-btn details-btn\">📊 Expenses</button>\n                                            </div>\n                                        </div>\n                                    ))}\n                                </div>\n                            </div>\n                        </div>\n                    )}\n\n                    {activeTab === 'categories' && (\n                        <div className=\"tab-content\">\n                            <div className=\"content-card\">\n                                <div className=\"card-header\">\n                                    <h2 className=\"card-title\">Expense Categories</h2>\n                                    <button className=\"add-btn\">➕ Add Category</button>\n                                </div>\n                                <div className=\"categories-list\">\n                                    {categories.map(category => (\n                                        <div key={category.id} className=\"category-item\">\n                                            <div className=\"category-info\">\n                                                <span className=\"category-name\">{category.name}</span>\n                                                <span className=\"category-count\">{category.count} expenses</span>\n                                            </div>\n                                            <div className=\"category-status\">\n                                                <span className={`status-badge ${category.active ? 'active' : 'inactive'}`}>\n                                                    {category.active ? 'Active' : 'Inactive'}\n                                                </span>\n                                            </div>\n                                            <div className=\"category-actions\">\n                                                <button className=\"action-btn edit-btn\">✏️ Edit</button>\n                                                <button className=\"action-btn toggle-btn\">\n                                                    {category.active ? '🔒 Deactivate' : '🔓 Activate'}\n                                                </button>\n                                            </div>\n                                        </div>\n                                    ))}\n                                </div>\n                            </div>\n                        </div>\n                    )}\n\n                    {activeTab === 'audit' && (\n                        <div className=\"tab-content\">\n                            <div className=\"content-card\">\n                                <h2 className=\"card-title\">Audit Trail</h2>\n                                <div className=\"audit-list\">\n                                    <div className=\"audit-item\">\n                                        <div className=\"audit-icon\">✅</div>\n                                        <div className=\"audit-info\">\n                                            <span className=\"audit-action\">Expense Approved</span>\n                                            <span className=\"audit-details\">John Doe's travel expense (₹1,200) approved</span>\n                                            <span className=\"audit-time\">2 hours ago</span>\n                                        </div>\n                                    </div>\n                                    <div className=\"audit-item\">\n                                        <div className=\"audit-icon\">❌</div>\n                                        <div className=\"audit-info\">\n                                            <span className=\"audit-action\">Expense Rejected</span>\n                                            <span className=\"audit-details\">Jane Smith's meal expense (₹850) rejected - Missing receipt</span>\n                                            <span className=\"audit-time\">4 hours ago</span>\n                                        </div>\n                                    </div>\n                                    <div className=\"audit-item\">\n                                        <div className=\"audit-icon\">📤</div>\n                                        <div className=\"audit-info\">\n                                            <span className=\"audit-action\">New Expense Submitted</span>\n                                            <span className=\"audit-details\">Mike Johnson submitted equipment expense (₹2,500)</span>\n                                            <span className=\"audit-time\">6 hours ago</span>\n                                        </div>\n                                    </div>\n                                </div>\n                            </div>\n                        </div>\n                    )}\n                </div>\n            </div>\n\n            {/* Reject Modal */}\n            {selectedExpense && (\n                <div className=\"modal-overlay\">\n                    <div className=\"modal-content\">\n                        <h3>Reject Expense</h3>\n                        <p>Rejecting expense from {selectedExpense.employee} for ₹{selectedExpense.amount}</p>\n                        <textarea\n                            className=\"form-input\"\n                            placeholder=\"Reason for rejection...\"\n                            value={rejectReason}\n                            onChange={(e) => setRejectReason(e.target.value)}\n                            rows=\"3\"\n                        />\n                        <div className=\"modal-actions\">\n                            <button \n                                className=\"action-btn reject-btn\"\n                                onClick={() => handleReject(selectedExpense.id)}\n                            >\n                                Reject Expense\n                            </button>\n                            <button \n                                className=\"action-btn cancel-btn\"\n                                onClick={() => setSelectedExpense(null)}\n                            >\n                                Cancel\n                            </button>\n                        </div>\n                    </div>\n                </div>\n            )}\n        </div>\n    );\n}\n\nexport default AdminDashboard;\n"], "mappings": ";;AAAA,SAASA,QAAQ,QAAQ,OAAO;AAChC,OAAO,iBAAiB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAEzB,SAASC,cAAcA,CAAA,EAAG;EAAAC,EAAA;EACtB,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAGR,QAAQ,CAAC,UAAU,CAAC;EACtD,MAAM,CAACS,QAAQ,EAAEC,WAAW,CAAC,GAAGV,QAAQ,CAAC,CACrC;IACIW,EAAE,EAAE,CAAC;IACLC,QAAQ,EAAE,UAAU;IACpBC,MAAM,EAAE,IAAI;IACZC,QAAQ,EAAE,QAAQ;IAClBC,WAAW,EAAE,kCAAkC;IAC/CC,IAAI,EAAE,YAAY;IAClBC,MAAM,EAAE,SAAS;IACjBC,OAAO,EAAE;EACb,CAAC,EACD;IACIP,EAAE,EAAE,CAAC;IACLC,QAAQ,EAAE,YAAY;IACtBC,MAAM,EAAE,GAAG;IACXC,QAAQ,EAAE,OAAO;IACjBC,WAAW,EAAE,uBAAuB;IACpCC,IAAI,EAAE,YAAY;IAClBC,MAAM,EAAE,UAAU;IAClBC,OAAO,EAAE;EACb,CAAC,EACD;IACIP,EAAE,EAAE,CAAC;IACLC,QAAQ,EAAE,cAAc;IACxBC,MAAM,EAAE,IAAI;IACZC,QAAQ,EAAE,WAAW;IACrBC,WAAW,EAAE,4BAA4B;IACzCC,IAAI,EAAE,YAAY;IAClBC,MAAM,EAAE,SAAS;IACjBC,OAAO,EAAE;EACb,CAAC,CACJ,CAAC;EAEF,MAAM,CAACC,KAAK,CAAC,GAAGnB,QAAQ,CAAC,CACrB;IAAEW,EAAE,EAAE,CAAC;IAAES,IAAI,EAAE,UAAU;IAAEC,KAAK,EAAE,kBAAkB;IAAEC,IAAI,EAAE,UAAU;IAAEC,UAAU,EAAE;EAAQ,CAAC,EAC7F;IAAEZ,EAAE,EAAE,CAAC;IAAES,IAAI,EAAE,YAAY;IAAEC,KAAK,EAAE,kBAAkB;IAAEC,IAAI,EAAE,UAAU;IAAEC,UAAU,EAAE;EAAY,CAAC,EACnG;IAAEZ,EAAE,EAAE,CAAC;IAAES,IAAI,EAAE,cAAc;IAAEC,KAAK,EAAE,kBAAkB;IAAEC,IAAI,EAAE,UAAU;IAAEC,UAAU,EAAE;EAAc,CAAC,EACvG;IAAEZ,EAAE,EAAE,CAAC;IAAES,IAAI,EAAE,cAAc;IAAEC,KAAK,EAAE,mBAAmB;IAAEC,IAAI,EAAE,SAAS;IAAEC,UAAU,EAAE;EAAU,CAAC,CACtG,CAAC;EAEF,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAGzB,QAAQ,CAAC,CACzC;IAAEW,EAAE,EAAE,CAAC;IAAES,IAAI,EAAE,QAAQ;IAAEM,MAAM,EAAE,IAAI;IAAEC,KAAK,EAAE;EAAG,CAAC,EAClD;IAAEhB,EAAE,EAAE,CAAC;IAAES,IAAI,EAAE,OAAO;IAAEM,MAAM,EAAE,IAAI;IAAEC,KAAK,EAAE;EAAE,CAAC,EAChD;IAAEhB,EAAE,EAAE,CAAC;IAAES,IAAI,EAAE,WAAW;IAAEM,MAAM,EAAE,IAAI;IAAEC,KAAK,EAAE;EAAG,CAAC,EACrD;IAAEhB,EAAE,EAAE,CAAC;IAAES,IAAI,EAAE,iBAAiB;IAAEM,MAAM,EAAE,IAAI;IAAEC,KAAK,EAAE;EAAE,CAAC,EAC1D;IAAEhB,EAAE,EAAE,CAAC;IAAES,IAAI,EAAE,UAAU;IAAEM,MAAM,EAAE,KAAK;IAAEC,KAAK,EAAE;EAAE,CAAC,CACvD,CAAC;EAEF,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAG7B,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAAC8B,eAAe,EAAEC,kBAAkB,CAAC,GAAG/B,QAAQ,CAAC,IAAI,CAAC;EAC5D,MAAM,CAACgC,YAAY,EAAEC,eAAe,CAAC,GAAGjC,QAAQ,CAAC,EAAE,CAAC;EAEpD,MAAMkC,aAAa,GAAIC,SAAS,IAAK;IACjCzB,WAAW,CAACD,QAAQ,CAAC2B,GAAG,CAACC,GAAG,IACxBA,GAAG,CAAC1B,EAAE,KAAKwB,SAAS,GAAG;MAAE,GAAGE,GAAG;MAAEpB,MAAM,EAAE;IAAW,CAAC,GAAGoB,GAC5D,CAAC,CAAC;IACFC,KAAK,CAAC,gCAAgC,CAAC;EAC3C,CAAC;EAED,MAAMC,YAAY,GAAIJ,SAAS,IAAK;IAChC,IAAI,CAACH,YAAY,CAACQ,IAAI,CAAC,CAAC,EAAE;MACtBF,KAAK,CAAC,uCAAuC,CAAC;MAC9C;IACJ;IACA5B,WAAW,CAACD,QAAQ,CAAC2B,GAAG,CAACC,GAAG,IACxBA,GAAG,CAAC1B,EAAE,KAAKwB,SAAS,GAAG;MAAE,GAAGE,GAAG;MAAEpB,MAAM,EAAE,UAAU;MAAEe;IAAa,CAAC,GAAGK,GAC1E,CAAC,CAAC;IACFN,kBAAkB,CAAC,IAAI,CAAC;IACxBE,eAAe,CAAC,EAAE,CAAC;IACnBK,KAAK,CAAC,gCAAgC,CAAC;EAC3C,CAAC;EAED,MAAMG,cAAc,GAAIxB,MAAM,IAAK;IAC/B,QAAQA,MAAM;MACV,KAAK,UAAU;QAAE,OAAO,wBAAwB;MAChD,KAAK,UAAU;QAAE,OAAO,yBAAyB;MACjD,KAAK,SAAS;QAAE,OAAO,uBAAuB;MAC9C;QAAS,OAAO,uBAAuB;IAC3C;EACJ,CAAC;EAED,MAAMyB,gBAAgB,GAAGd,YAAY,KAAK,KAAK,GACzCnB,QAAQ,GACRA,QAAQ,CAACkC,MAAM,CAACN,GAAG,IAAIA,GAAG,CAACpB,MAAM,KAAKW,YAAY,CAAC;EAEzD,MAAMgB,IAAI,GAAG,CACT;IAAEjC,EAAE,EAAE,UAAU;IAAEkC,KAAK,EAAE,cAAc;IAAEC,IAAI,EAAE;EAAK,CAAC,EACrD;IAAEnC,EAAE,EAAE,OAAO;IAAEkC,KAAK,EAAE,iBAAiB;IAAEC,IAAI,EAAE;EAAK,CAAC,EACrD;IAAEnC,EAAE,EAAE,YAAY;IAAEkC,KAAK,EAAE,YAAY;IAAEC,IAAI,EAAE;EAAM,CAAC,EACtD;IAAEnC,EAAE,EAAE,OAAO;IAAEkC,KAAK,EAAE,aAAa;IAAEC,IAAI,EAAE;EAAK,CAAC,CACpD;EAED,oBACI5C,OAAA;IAAK6C,SAAS,EAAC,qBAAqB;IAAAC,QAAA,gBAChC9C,OAAA;MAAK6C,SAAS,EAAC,sBAAsB;MAAAC,QAAA,eACjC9C,OAAA;QAAK6C,SAAS,EAAC;MAAqB;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC1C,CAAC,eAENlD,OAAA;MAAK6C,SAAS,EAAC,mBAAmB;MAAAC,QAAA,gBAC9B9C,OAAA;QAAK6C,SAAS,EAAC,kBAAkB;QAAAC,QAAA,gBAC7B9C,OAAA;UAAI6C,SAAS,EAAC,iBAAiB;UAAAC,QAAA,GAAC,QACtB,eAAA9C,OAAA;YAAM6C,SAAS,EAAC,eAAe;YAAAC,QAAA,EAAC;UAAS;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtD,CAAC,eACLlD,OAAA;UAAG6C,SAAS,EAAC,oBAAoB;UAAAC,QAAA,EAAC;QAA2C;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAChF,CAAC,eAENlD,OAAA;QAAK6C,SAAS,EAAC,iBAAiB;QAAAC,QAAA,gBAC5B9C,OAAA;UAAK6C,SAAS,EAAC,WAAW;UAAAC,QAAA,gBACtB9C,OAAA;YAAK6C,SAAS,EAAC,WAAW;YAAAC,QAAA,EAAC;UAAE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACnClD,OAAA;YAAK6C,SAAS,EAAC,WAAW;YAAAC,QAAA,gBACtB9C,OAAA;cAAM6C,SAAS,EAAC,YAAY;cAAAC,QAAA,EAAC;YAAE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACtClD,OAAA;cAAM6C,SAAS,EAAC,YAAY;cAAAC,QAAA,EAAC;YAAc;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,eACNlD,OAAA;UAAK6C,SAAS,EAAC,WAAW;UAAAC,QAAA,gBACtB9C,OAAA;YAAK6C,SAAS,EAAC,WAAW;YAAAC,QAAA,EAAC;UAAC;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eAClClD,OAAA;YAAK6C,SAAS,EAAC,WAAW;YAAAC,QAAA,gBACtB9C,OAAA;cAAM6C,SAAS,EAAC,YAAY;cAAAC,QAAA,EAAC;YAAC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACrClD,OAAA;cAAM6C,SAAS,EAAC,YAAY;cAAAC,QAAA,EAAC;YAAc;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,eACNlD,OAAA;UAAK6C,SAAS,EAAC,WAAW;UAAAC,QAAA,gBACtB9C,OAAA;YAAK6C,SAAS,EAAC,WAAW;YAAAC,QAAA,EAAC;UAAC;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eAClClD,OAAA;YAAK6C,SAAS,EAAC,WAAW;YAAAC,QAAA,gBACtB9C,OAAA;cAAM6C,SAAS,EAAC,YAAY;cAAAC,QAAA,EAAC;YAAE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACtClD,OAAA;cAAM6C,SAAS,EAAC,YAAY;cAAAC,QAAA,EAAC;YAAQ;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3C,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,eACNlD,OAAA;UAAK6C,SAAS,EAAC,WAAW;UAAAC,QAAA,gBACtB9C,OAAA;YAAK6C,SAAS,EAAC,WAAW;YAAAC,QAAA,EAAC;UAAE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACnClD,OAAA;YAAK6C,SAAS,EAAC,WAAW;YAAAC,QAAA,gBACtB9C,OAAA;cAAM6C,SAAS,EAAC,YAAY;cAAAC,QAAA,EAAC;YAAO;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAC3ClD,OAAA;cAAM6C,SAAS,EAAC,YAAY;cAAAC,QAAA,EAAC;YAAY;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/C,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,eAENlD,OAAA;QAAK6C,SAAS,EAAC,gBAAgB;QAAAC,QAAA,EAC1BJ,IAAI,CAACR,GAAG,CAACiB,GAAG,iBACTnD,OAAA;UAEI6C,SAAS,EAAE,WAAWxC,SAAS,KAAK8C,GAAG,CAAC1C,EAAE,GAAG,QAAQ,GAAG,EAAE,EAAG;UAC7D2C,OAAO,EAAEA,CAAA,KAAM9C,YAAY,CAAC6C,GAAG,CAAC1C,EAAE,CAAE;UAAAqC,QAAA,gBAEpC9C,OAAA;YAAM6C,SAAS,EAAC,UAAU;YAAAC,QAAA,EAAEK,GAAG,CAACP;UAAI;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,EAC3CC,GAAG,CAACR,KAAK;QAAA,GALLQ,GAAG,CAAC1C,EAAE;UAAAsC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAMP,CACX;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,eAENlD,OAAA;QAAK6C,SAAS,EAAC,gBAAgB;QAAAC,QAAA,GAC1BzC,SAAS,KAAK,UAAU,iBACrBL,OAAA;UAAK6C,SAAS,EAAC,aAAa;UAAAC,QAAA,eACxB9C,OAAA;YAAK6C,SAAS,EAAC,cAAc;YAAAC,QAAA,gBACzB9C,OAAA;cAAK6C,SAAS,EAAC,aAAa;cAAAC,QAAA,gBACxB9C,OAAA;gBAAI6C,SAAS,EAAC,YAAY;gBAAAC,QAAA,EAAC;cAAsB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACtDlD,OAAA;gBAAK6C,SAAS,EAAC,iBAAiB;gBAAAC,QAAA,eAC5B9C,OAAA;kBACI6C,SAAS,EAAC,eAAe;kBACzBQ,KAAK,EAAE3B,YAAa;kBACpB4B,QAAQ,EAAGC,CAAC,IAAK5B,eAAe,CAAC4B,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;kBAAAP,QAAA,gBAEjD9C,OAAA;oBAAQqD,KAAK,EAAC,KAAK;oBAAAP,QAAA,EAAC;kBAAU;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eACvClD,OAAA;oBAAQqD,KAAK,EAAC,SAAS;oBAAAP,QAAA,EAAC;kBAAO;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eACxClD,OAAA;oBAAQqD,KAAK,EAAC,UAAU;oBAAAP,QAAA,EAAC;kBAAQ;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eAC1ClD,OAAA;oBAAQqD,KAAK,EAAC,UAAU;oBAAAP,QAAA,EAAC;kBAAQ;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACtC;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACR,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC,eACNlD,OAAA;cAAK6C,SAAS,EAAC,gBAAgB;cAAAC,QAAA,gBAC3B9C,OAAA;gBAAK6C,SAAS,EAAC,cAAc;gBAAAC,QAAA,gBACzB9C,OAAA;kBAAA8C,QAAA,EAAM;gBAAQ;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACrBlD,OAAA;kBAAA8C,QAAA,EAAM;gBAAM;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACnBlD,OAAA;kBAAA8C,QAAA,EAAM;gBAAQ;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACrBlD,OAAA;kBAAA8C,QAAA,EAAM;gBAAI;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACjBlD,OAAA;kBAAA8C,QAAA,EAAM;gBAAM;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACnBlD,OAAA;kBAAA8C,QAAA,EAAM;gBAAO;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnB,CAAC,EACLV,gBAAgB,CAACN,GAAG,CAACuB,OAAO,iBACzBzD,OAAA;gBAAsB6C,SAAS,EAAC,WAAW;gBAAAC,QAAA,gBACvC9C,OAAA;kBAAM6C,SAAS,EAAC,eAAe;kBAAAC,QAAA,EAAEW,OAAO,CAAC/C;gBAAQ;kBAAAqC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACzDlD,OAAA;kBAAM6C,SAAS,EAAC,gBAAgB;kBAAAC,QAAA,GAAC,QAAC,EAACW,OAAO,CAAC9C,MAAM;gBAAA;kBAAAoC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACzDlD,OAAA;kBAAM6C,SAAS,EAAC,kBAAkB;kBAAAC,QAAA,EAAEW,OAAO,CAAC7C;gBAAQ;kBAAAmC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAC5DlD,OAAA;kBAAM6C,SAAS,EAAC,cAAc;kBAAAC,QAAA,EAAEW,OAAO,CAAC3C;gBAAI;kBAAAiC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACpDlD,OAAA;kBACI6C,SAAS,EAAC,gBAAgB;kBAC1Ba,KAAK,EAAE;oBAAEC,KAAK,EAAEpB,cAAc,CAACkB,OAAO,CAAC1C,MAAM;kBAAE,CAAE;kBAAA+B,QAAA,EAEhDW,OAAO,CAAC1C;gBAAM;kBAAAgC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACb,CAAC,eACPlD,OAAA;kBAAK6C,SAAS,EAAC,eAAe;kBAAAC,QAAA,GACzBW,OAAO,CAAC1C,MAAM,KAAK,SAAS,iBACzBf,OAAA,CAAAE,SAAA;oBAAA4C,QAAA,gBACI9C,OAAA;sBACI6C,SAAS,EAAC,wBAAwB;sBAClCO,OAAO,EAAEA,CAAA,KAAMpB,aAAa,CAACyB,OAAO,CAAChD,EAAE,CAAE;sBAAAqC,QAAA,EAC5C;oBAED;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,eACTlD,OAAA;sBACI6C,SAAS,EAAC,uBAAuB;sBACjCO,OAAO,EAAEA,CAAA,KAAMvB,kBAAkB,CAAC4B,OAAO,CAAE;sBAAAX,QAAA,EAC9C;oBAED;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC;kBAAA,eACX,CACL,EACAO,OAAO,CAACzC,OAAO,iBACZhB,OAAA;oBAAQ6C,SAAS,EAAC,qBAAqB;oBAAAC,QAAA,EAAC;kBAAe;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAClE,eACDlD,OAAA;oBAAQ6C,SAAS,EAAC,wBAAwB;oBAAAC,QAAA,EAAC;kBAAW;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC9D,CAAC;cAAA,GAhCAO,OAAO,CAAChD,EAAE;gBAAAsC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAiCf,CACR,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CACR,EAEA7C,SAAS,KAAK,OAAO,iBAClBL,OAAA;UAAK6C,SAAS,EAAC,aAAa;UAAAC,QAAA,eACxB9C,OAAA;YAAK6C,SAAS,EAAC,cAAc;YAAAC,QAAA,gBACzB9C,OAAA;cAAI6C,SAAS,EAAC,YAAY;cAAAC,QAAA,EAAC;YAAe;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC/ClD,OAAA;cAAK6C,SAAS,EAAC,YAAY;cAAAC,QAAA,EACtB7B,KAAK,CAACiB,GAAG,CAAC0B,IAAI,iBACX5D,OAAA;gBAAmB6C,SAAS,EAAC,WAAW;gBAAAC,QAAA,gBACpC9C,OAAA;kBAAK6C,SAAS,EAAC,aAAa;kBAAAC,QAAA,EAAC;gBAAE;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACrClD,OAAA;kBAAK6C,SAAS,EAAC,WAAW;kBAAAC,QAAA,gBACtB9C,OAAA;oBAAI6C,SAAS,EAAC,WAAW;oBAAAC,QAAA,EAAEc,IAAI,CAAC1C;kBAAI;oBAAA6B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eAC1ClD,OAAA;oBAAG6C,SAAS,EAAC,YAAY;oBAAAC,QAAA,EAAEc,IAAI,CAACzC;kBAAK;oBAAA4B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAC1ClD,OAAA;oBAAK6C,SAAS,EAAC,WAAW;oBAAAC,QAAA,gBACtB9C,OAAA;sBAAM6C,SAAS,EAAC,WAAW;sBAAAC,QAAA,EAAEc,IAAI,CAACxC;oBAAI;sBAAA2B,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC,eAC9ClD,OAAA;sBAAM6C,SAAS,EAAC,iBAAiB;sBAAAC,QAAA,EAAEc,IAAI,CAACvC;oBAAU;sBAAA0B,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACzD,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACL,CAAC,eACNlD,OAAA;kBAAK6C,SAAS,EAAC,cAAc;kBAAAC,QAAA,gBACzB9C,OAAA;oBAAQ6C,SAAS,EAAC,qBAAqB;oBAAAC,QAAA,EAAC;kBAAgB;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eACjElD,OAAA;oBAAQ6C,SAAS,EAAC,wBAAwB;oBAAAC,QAAA,EAAC;kBAAW;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC9D,CAAC;cAAA,GAbAU,IAAI,CAACnD,EAAE;gBAAAsC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAcZ,CACR;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CACR,EAEA7C,SAAS,KAAK,YAAY,iBACvBL,OAAA;UAAK6C,SAAS,EAAC,aAAa;UAAAC,QAAA,eACxB9C,OAAA;YAAK6C,SAAS,EAAC,cAAc;YAAAC,QAAA,gBACzB9C,OAAA;cAAK6C,SAAS,EAAC,aAAa;cAAAC,QAAA,gBACxB9C,OAAA;gBAAI6C,SAAS,EAAC,YAAY;gBAAAC,QAAA,EAAC;cAAkB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAClDlD,OAAA;gBAAQ6C,SAAS,EAAC,SAAS;gBAAAC,QAAA,EAAC;cAAc;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClD,CAAC,eACNlD,OAAA;cAAK6C,SAAS,EAAC,iBAAiB;cAAAC,QAAA,EAC3BxB,UAAU,CAACY,GAAG,CAACtB,QAAQ,iBACpBZ,OAAA;gBAAuB6C,SAAS,EAAC,eAAe;gBAAAC,QAAA,gBAC5C9C,OAAA;kBAAK6C,SAAS,EAAC,eAAe;kBAAAC,QAAA,gBAC1B9C,OAAA;oBAAM6C,SAAS,EAAC,eAAe;oBAAAC,QAAA,EAAElC,QAAQ,CAACM;kBAAI;oBAAA6B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eACtDlD,OAAA;oBAAM6C,SAAS,EAAC,gBAAgB;oBAAAC,QAAA,GAAElC,QAAQ,CAACa,KAAK,EAAC,WAAS;kBAAA;oBAAAsB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChE,CAAC,eACNlD,OAAA;kBAAK6C,SAAS,EAAC,iBAAiB;kBAAAC,QAAA,eAC5B9C,OAAA;oBAAM6C,SAAS,EAAE,gBAAgBjC,QAAQ,CAACY,MAAM,GAAG,QAAQ,GAAG,UAAU,EAAG;oBAAAsB,QAAA,EACtElC,QAAQ,CAACY,MAAM,GAAG,QAAQ,GAAG;kBAAU;oBAAAuB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACtC;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN,CAAC,eACNlD,OAAA;kBAAK6C,SAAS,EAAC,kBAAkB;kBAAAC,QAAA,gBAC7B9C,OAAA;oBAAQ6C,SAAS,EAAC,qBAAqB;oBAAAC,QAAA,EAAC;kBAAO;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eACxDlD,OAAA;oBAAQ6C,SAAS,EAAC,uBAAuB;oBAAAC,QAAA,EACpClC,QAAQ,CAACY,MAAM,GAAG,eAAe,GAAG;kBAAa;oBAAAuB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC9C,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACR,CAAC;cAAA,GAfAtC,QAAQ,CAACH,EAAE;gBAAAsC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAgBhB,CACR;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CACR,EAEA7C,SAAS,KAAK,OAAO,iBAClBL,OAAA;UAAK6C,SAAS,EAAC,aAAa;UAAAC,QAAA,eACxB9C,OAAA;YAAK6C,SAAS,EAAC,cAAc;YAAAC,QAAA,gBACzB9C,OAAA;cAAI6C,SAAS,EAAC,YAAY;cAAAC,QAAA,EAAC;YAAW;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC3ClD,OAAA;cAAK6C,SAAS,EAAC,YAAY;cAAAC,QAAA,gBACvB9C,OAAA;gBAAK6C,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACvB9C,OAAA;kBAAK6C,SAAS,EAAC,YAAY;kBAAAC,QAAA,EAAC;gBAAC;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACnClD,OAAA;kBAAK6C,SAAS,EAAC,YAAY;kBAAAC,QAAA,gBACvB9C,OAAA;oBAAM6C,SAAS,EAAC,cAAc;oBAAAC,QAAA,EAAC;kBAAgB;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eACtDlD,OAAA;oBAAM6C,SAAS,EAAC,eAAe;oBAAAC,QAAA,EAAC;kBAA2C;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eAClFlD,OAAA;oBAAM6C,SAAS,EAAC,YAAY;oBAAAC,QAAA,EAAC;kBAAW;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC9C,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC,eACNlD,OAAA;gBAAK6C,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACvB9C,OAAA;kBAAK6C,SAAS,EAAC,YAAY;kBAAAC,QAAA,EAAC;gBAAC;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACnClD,OAAA;kBAAK6C,SAAS,EAAC,YAAY;kBAAAC,QAAA,gBACvB9C,OAAA;oBAAM6C,SAAS,EAAC,cAAc;oBAAAC,QAAA,EAAC;kBAAgB;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eACtDlD,OAAA;oBAAM6C,SAAS,EAAC,eAAe;oBAAAC,QAAA,EAAC;kBAA2D;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eAClGlD,OAAA;oBAAM6C,SAAS,EAAC,YAAY;oBAAAC,QAAA,EAAC;kBAAW;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC9C,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC,eACNlD,OAAA;gBAAK6C,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACvB9C,OAAA;kBAAK6C,SAAS,EAAC,YAAY;kBAAAC,QAAA,EAAC;gBAAE;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACpClD,OAAA;kBAAK6C,SAAS,EAAC,YAAY;kBAAAC,QAAA,gBACvB9C,OAAA;oBAAM6C,SAAS,EAAC,cAAc;oBAAAC,QAAA,EAAC;kBAAqB;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eAC3DlD,OAAA;oBAAM6C,SAAS,EAAC,eAAe;oBAAAC,QAAA,EAAC;kBAAiD;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eACxFlD,OAAA;oBAAM6C,SAAS,EAAC,YAAY;oBAAAC,QAAA,EAAC;kBAAW;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC9C,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CACR;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC,EAGLtB,eAAe,iBACZ5B,OAAA;MAAK6C,SAAS,EAAC,eAAe;MAAAC,QAAA,eAC1B9C,OAAA;QAAK6C,SAAS,EAAC,eAAe;QAAAC,QAAA,gBAC1B9C,OAAA;UAAA8C,QAAA,EAAI;QAAc;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACvBlD,OAAA;UAAA8C,QAAA,GAAG,yBAAuB,EAAClB,eAAe,CAAClB,QAAQ,EAAC,aAAM,EAACkB,eAAe,CAACjB,MAAM;QAAA;UAAAoC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACtFlD,OAAA;UACI6C,SAAS,EAAC,YAAY;UACtBgB,WAAW,EAAC,yBAAyB;UACrCR,KAAK,EAAEvB,YAAa;UACpBwB,QAAQ,EAAGC,CAAC,IAAKxB,eAAe,CAACwB,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;UACjDS,IAAI,EAAC;QAAG;UAAAf,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACX,CAAC,eACFlD,OAAA;UAAK6C,SAAS,EAAC,eAAe;UAAAC,QAAA,gBAC1B9C,OAAA;YACI6C,SAAS,EAAC,uBAAuB;YACjCO,OAAO,EAAEA,CAAA,KAAMf,YAAY,CAACT,eAAe,CAACnB,EAAE,CAAE;YAAAqC,QAAA,EACnD;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACTlD,OAAA;YACI6C,SAAS,EAAC,uBAAuB;YACjCO,OAAO,EAAEA,CAAA,KAAMvB,kBAAkB,CAAC,IAAI,CAAE;YAAAiB,QAAA,EAC3C;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CACR;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACA,CAAC;AAEd;AAAC9C,EAAA,CA5VQD,cAAc;AAAA4D,EAAA,GAAd5D,cAAc;AA8VvB,eAAeA,cAAc;AAAC,IAAA4D,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}