{"ast": null, "code": "var _jsxFileName = \"D:\\\\Expense Tracker App (MERN )\\\\expensetracker\\\\src\\\\components\\\\home\\\\CTA.js\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction CTA() {\n  return /*#__PURE__*/_jsxDEV(\"section\", {\n    className: \"container py-5 text-center\",\n    children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n      className: \"text-primary\",\n      children: \"Start Managing Your Money Today\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 4,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n      href: \"/signup\",\n      className: \"btn btn-primary btn-lg mt-3\",\n      children: \"Create Free Account\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 5,\n      columnNumber: 13\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 3,\n    columnNumber: 9\n  }, this);\n}\n_c = CTA;\nexport default CTA;\nvar _c;\n$RefreshReg$(_c, \"CTA\");", "map": {"version": 3, "names": ["CTA", "_jsxDEV", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "href", "_c", "$RefreshReg$"], "sources": ["D:/Expense Tracker App (MERN )/expensetracker/src/components/home/<USER>"], "sourcesContent": ["function CTA() {\r\n    return (\r\n        <section className=\"container py-5 text-center\">\r\n            <h2 className=\"text-primary\">Start Managing Your Money Today</h2>\r\n            <a href=\"/signup\" className=\"btn btn-primary btn-lg mt-3\">Create Free Account</a>\r\n        </section>\r\n    );\r\n}\r\nexport default CTA;"], "mappings": ";;AAAA,SAASA,GAAGA,CAAA,EAAG;EACX,oBACIC,OAAA;IAASC,SAAS,EAAC,4BAA4B;IAAAC,QAAA,gBAC3CF,OAAA;MAAIC,SAAS,EAAC,cAAc;MAAAC,QAAA,EAAC;IAA+B;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,eACjEN,OAAA;MAAGO,IAAI,EAAC,SAAS;MAACN,SAAS,EAAC,6BAA6B;MAAAC,QAAA,EAAC;IAAmB;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAG,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAC5E,CAAC;AAElB;AAACE,EAAA,GAPQT,GAAG;AAQZ,eAAeA,GAAG;AAAC,IAAAS,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}