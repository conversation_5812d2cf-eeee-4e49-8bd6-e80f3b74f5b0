{"ast": null, "code": "var _jsxFileName = \"D:\\\\Expense Tracker App (MERN )\\\\expensetracker\\\\src\\\\components\\\\home\\\\UseCases.js\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction UseCases() {\n  return /*#__PURE__*/_jsxDEV(\"section\", {\n    className: \"container py-5 text-center\",\n    children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n      className: \"text-info mb-4\",\n      children: \"Who Is It For?\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 4,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n      children: \"\\uD83D\\uDCBC Professionals, \\uD83D\\uDC68\\u200D\\uD83C\\uDF93 Students, \\uD83C\\uDFE0 Families \\u2013 anyone who wants financial clarity!\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 5,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"blockquote\", {\n      className: \"blockquote\",\n      children: [/*#__PURE__*/_jsxDEV(\"p\", {\n        children: \"\\\"This app helped me save 20% more every month!\\\"\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 7,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"footer\", {\n        className: \"blockquote-footer text-light\",\n        children: \"\\u2013 A Happy User\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 8,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 6,\n      columnNumber: 13\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 3,\n    columnNumber: 9\n  }, this);\n}\n_c = UseCases;\nexport default UseCases;\nvar _c;\n$RefreshReg$(_c, \"UseCases\");", "map": {"version": 3, "names": ["UseCases", "_jsxDEV", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["D:/Expense Tracker App (MERN )/expensetracker/src/components/home/<USER>"], "sourcesContent": ["function UseCases() {\r\n    return (\r\n        <section className=\"container py-5 text-center\">\r\n            <h2 className=\"text-info mb-4\">Who Is It For?</h2>\r\n            <p>💼 Professionals, 👨‍🎓 Students, 🏠 Families – anyone who wants financial clarity!</p>\r\n            <blockquote className=\"blockquote\">\r\n                <p>\"This app helped me save 20% more every month!\"</p>\r\n                <footer className=\"blockquote-footer text-light\">– A Happy User</footer>\r\n            </blockquote>\r\n        </section>\r\n    );\r\n}\r\nexport default UseCases;"], "mappings": ";;AAAA,SAASA,QAAQA,CAAA,EAAG;EAChB,oBACIC,OAAA;IAASC,SAAS,EAAC,4BAA4B;IAAAC,QAAA,gBAC3CF,OAAA;MAAIC,SAAS,EAAC,gBAAgB;MAAAC,QAAA,EAAC;IAAc;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,eAClDN,OAAA;MAAAE,QAAA,EAAG;IAAmF;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAG,CAAC,eAC1FN,OAAA;MAAYC,SAAS,EAAC,YAAY;MAAAC,QAAA,gBAC9BF,OAAA;QAAAE,QAAA,EAAG;MAA+C;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC,eACtDN,OAAA;QAAQC,SAAS,EAAC,8BAA8B;QAAAC,QAAA,EAAC;MAAc;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAChE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACR,CAAC;AAElB;AAACC,EAAA,GAXQR,QAAQ;AAYjB,eAAeA,QAAQ;AAAC,IAAAQ,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}