.cta-section {
  position: relative;
  padding: 120px 0;
  background: linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 50%, #16213e 100%);
  overflow: hidden;
  text-align: center;
}

.cta-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 0;
}

.cta-particles {
  position: absolute;
  width: 100%;
  height: 100%;
  background: 
    radial-gradient(circle at 20% 30%, rgba(0, 212, 255, 0.15) 0%, transparent 50%),
    radial-gradient(circle at 80% 70%, rgba(255, 107, 107, 0.15) 0%, transparent 50%),
    radial-gradient(circle at 50% 50%, rgba(78, 205, 196, 0.1) 0%, transparent 50%);
  animation: ctaFloat 25s ease-in-out infinite;
}

@keyframes ctaFloat {
  0%, 100% {
    transform: translateY(0px) rotate(0deg) scale(1);
  }
  33% {
    transform: translateY(-30px) rotate(2deg) scale(1.05);
  }
  66% {
    transform: translateY(15px) rotate(-1deg) scale(0.95);
  }
}

.cta-content {
  position: relative;
  z-index: 1;
  max-width: 800px;
  margin: 0 auto;
  padding: 0 2rem;
}

.cta-badge {
  display: inline-block;
  background: rgba(255, 107, 107, 0.1);
  border: 1px solid rgba(255, 107, 107, 0.3);
  border-radius: 50px;
  padding: 10px 25px;
  margin-bottom: 2rem;
  font-size: 1rem;
  color: var(--accent-secondary);
  backdrop-filter: blur(10px);
  animation: pulse 2s infinite;
}

.cta-title {
  font-size: clamp(2.5rem, 6vw, 4.5rem);
  font-weight: 800;
  line-height: 1.1;
  margin-bottom: 1.5rem;
  color: var(--text-primary);
  text-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
}

.cta-subtitle {
  font-size: clamp(1.1rem, 2.5vw, 1.3rem);
  color: var(--text-secondary);
  margin-bottom: 3rem;
  line-height: 1.6;
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
}

.cta-buttons {
  display: flex;
  gap: 1.5rem;
  justify-content: center;
  align-items: center;
  margin-bottom: 3rem;
  flex-wrap: wrap;
}

.cta-primary-btn {
  background: var(--gradient-tertiary);
  padding: 20px 45px;
  border-radius: 50px;
  color: white;
  text-decoration: none;
  font-weight: 600;
  font-size: 1.2rem;
  display: inline-flex;
  align-items: center;
  gap: 12px;
  transition: var(--transition);
  box-shadow: 0 10px 35px rgba(79, 172, 254, 0.4);
  position: relative;
  overflow: hidden;
}

.cta-primary-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  transition: left 0.6s;
}

.cta-primary-btn:hover::before {
  left: 100%;
}

.cta-primary-btn:hover {
  transform: translateY(-3px);
  box-shadow: 0 15px 45px rgba(79, 172, 254, 0.5);
  text-decoration: none;
  color: white;
}

.cta-secondary-btn {
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(15px);
  border: 1px solid rgba(255, 255, 255, 0.15);
  padding: 20px 40px;
  border-radius: 50px;
  color: var(--text-primary);
  text-decoration: none;
  font-weight: 500;
  font-size: 1.1rem;
  display: inline-flex;
  align-items: center;
  gap: 12px;
  transition: var(--transition);
}

.cta-secondary-btn:hover {
  background: rgba(255, 255, 255, 0.1);
  border-color: rgba(255, 255, 255, 0.25);
  transform: translateY(-2px);
  text-decoration: none;
  color: var(--text-primary);
}

.btn-icon {
  font-size: 1.3rem;
  transition: var(--transition);
}

.cta-primary-btn:hover .btn-icon {
  transform: scale(1.2) rotate(10deg);
}

.cta-secondary-btn:hover .btn-icon {
  transform: scale(1.1);
}

.cta-features {
  display: flex;
  justify-content: center;
  gap: 2rem;
  margin-bottom: 3rem;
  flex-wrap: wrap;
}

.feature-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: var(--text-secondary);
  font-size: 1rem;
}

.feature-icon {
  color: var(--accent-tertiary);
  font-size: 1.2rem;
}

.cta-trust {
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  padding-top: 2rem;
}

.trust-text {
  color: var(--text-secondary);
  margin-bottom: 1rem;
  font-size: 1rem;
}

.trust-badges {
  display: flex;
  justify-content: center;
  gap: 1.5rem;
  flex-wrap: wrap;
}

.trust-badge {
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  padding: 8px 16px;
  border-radius: 25px;
  font-size: 0.9rem;
  color: var(--text-secondary);
  transition: var(--transition);
}

.trust-badge:hover {
  background: rgba(255, 255, 255, 0.08);
  border-color: rgba(255, 255, 255, 0.15);
  color: var(--text-primary);
}

/* Responsive design */
@media (max-width: 768px) {
  .cta-section {
    padding: 80px 0;
  }
  
  .cta-buttons {
    flex-direction: column;
    gap: 1rem;
  }
  
  .cta-primary-btn,
  .cta-secondary-btn {
    width: 100%;
    max-width: 300px;
    justify-content: center;
  }
  
  .cta-features {
    flex-direction: column;
    gap: 1rem;
  }
  
  .trust-badges {
    flex-direction: column;
    gap: 1rem;
    align-items: center;
  }
}

@media (max-width: 480px) {
  .cta-content {
    padding: 0 1rem;
  }
  
  .cta-primary-btn,
  .cta-secondary-btn {
    padding: 18px 35px;
    font-size: 1rem;
  }
}
