{"ast": null, "code": "var _jsxFileName = \"D:\\\\Expense Tracker App (MERN )\\\\expensetracker\\\\src\\\\pages\\\\Contact.js\",\n  _s = $RefreshSig$();\nimport { useState } from 'react';\nimport './Contact.css';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nfunction Contact() {\n  _s();\n  const [formData, setFormData] = useState({\n    name: '',\n    email: '',\n    subject: '',\n    message: ''\n  });\n  const [errors, setErrors] = useState({});\n  const [isLoading, setIsLoading] = useState(false);\n  const [isSubmitted, setIsSubmitted] = useState(false);\n  const handleChange = e => {\n    const {\n      name,\n      value\n    } = e.target;\n    setFormData(prev => ({\n      ...prev,\n      [name]: value\n    }));\n    // Clear error when user starts typing\n    if (errors[name]) {\n      setErrors(prev => ({\n        ...prev,\n        [name]: ''\n      }));\n    }\n  };\n  const validateForm = () => {\n    const newErrors = {};\n    if (!formData.name.trim()) {\n      newErrors.name = 'Name is required';\n    }\n    if (!formData.email) {\n      newErrors.email = 'Email is required';\n    } else if (!/\\S+@\\S+\\.\\S+/.test(formData.email)) {\n      newErrors.email = 'Email is invalid';\n    }\n    if (!formData.subject.trim()) {\n      newErrors.subject = 'Subject is required';\n    }\n    if (!formData.message.trim()) {\n      newErrors.message = 'Message is required';\n    } else if (formData.message.trim().length < 10) {\n      newErrors.message = 'Message must be at least 10 characters';\n    }\n    setErrors(newErrors);\n    return Object.keys(newErrors).length === 0;\n  };\n  const handleSubmit = async e => {\n    e.preventDefault();\n    if (!validateForm()) {\n      return;\n    }\n    setIsLoading(true);\n    try {\n      // Simulate API call\n      await new Promise(resolve => setTimeout(resolve, 2000));\n      console.log('Contact form data:', formData);\n      setIsSubmitted(true);\n      setFormData({\n        name: '',\n        email: '',\n        subject: '',\n        message: ''\n      });\n    } catch (error) {\n      console.error('Contact form error:', error);\n      alert('Failed to send message. Please try again.');\n    } finally {\n      setIsLoading(false);\n    }\n  };\n  const contactInfo = [{\n    icon: \"📧\",\n    title: \"Email Us\",\n    info: \"<EMAIL>\",\n    description: \"Get in touch for support or general inquiries\"\n  }, {\n    icon: \"📞\",\n    title: \"Call Us\",\n    info: \"+1 (555) 123-4567\",\n    description: \"Mon-Fri 9AM-6PM EST\"\n  }, {\n    icon: \"📍\",\n    title: \"Visit Us\",\n    info: \"123 Tech Street, Silicon Valley, CA 94000\",\n    description: \"Our headquarters - visitors welcome!\"\n  }, {\n    icon: \"💬\",\n    title: \"Live Chat\",\n    info: \"Available 24/7\",\n    description: \"Instant support through our website\"\n  }];\n  const faqs = [{\n    question: \"How secure is my financial data?\",\n    answer: \"We use bank-level encryption and never store your banking credentials. All data is encrypted both in transit and at rest.\"\n  }, {\n    question: \"Can I export my expense data?\",\n    answer: \"Yes! You can export your data in CSV, PDF, or Excel format anytime from your dashboard.\"\n  }, {\n    question: \"Is there a mobile app?\",\n    answer: \"Our web app is fully responsive and works great on mobile. Native iOS and Android apps are coming soon!\"\n  }, {\n    question: \"What payment methods do you accept?\",\n    answer: \"We accept all major credit cards, PayPal, and bank transfers. All payments are processed securely.\"\n  }];\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"contact-container\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"contact-background\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"contact-particles\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 130,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 129,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n      className: \"contact-hero\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"container\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"hero-content fade-in-up\",\n          children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n            className: \"contact-title\",\n            children: [\"Get In \", /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"gradient-text\",\n              children: \"Touch\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 138,\n              columnNumber: 36\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 137,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"contact-subtitle\",\n            children: \"Have questions? We'd love to hear from you. Send us a message and we'll respond as soon as possible.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 140,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 136,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 135,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 134,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n      className: \"contact-main\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"container\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"contact-grid\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"contact-info\",\n            children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n              className: \"info-title fade-in-up\",\n              children: \"Contact Information\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 154,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"info-subtitle fade-in-up\",\n              children: \"Choose the best way to reach us\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 155,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"contact-methods\",\n              children: contactInfo.map((item, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"contact-method glass-card fade-in-up\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"method-icon\",\n                  children: item.icon\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 162,\n                  columnNumber: 41\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"method-content\",\n                  children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                    className: \"method-title\",\n                    children: item.title\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 164,\n                    columnNumber: 45\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"method-info\",\n                    children: item.info\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 165,\n                    columnNumber: 45\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"method-description\",\n                    children: item.description\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 166,\n                    columnNumber: 45\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 163,\n                  columnNumber: 41\n                }, this)]\n              }, index, true, {\n                fileName: _jsxFileName,\n                lineNumber: 161,\n                columnNumber: 37\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 159,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 153,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"contact-form-section\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-card glass-card\",\n              children: isSubmitted ? /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"success-message fade-in-up\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"success-icon\",\n                  children: \"\\u2705\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 178,\n                  columnNumber: 41\n                }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                  children: \"Message Sent Successfully!\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 179,\n                  columnNumber: 41\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  children: \"Thank you for contacting us. We'll get back to you within 24 hours.\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 180,\n                  columnNumber: 41\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: () => setIsSubmitted(false),\n                  className: \"send-another-btn\",\n                  children: \"Send Another Message\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 181,\n                  columnNumber: 41\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 177,\n                columnNumber: 37\n              }, this) : /*#__PURE__*/_jsxDEV(_Fragment, {\n                children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n                  className: \"form-title\",\n                  children: \"Send us a Message\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 190,\n                  columnNumber: 41\n                }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n                  onSubmit: handleSubmit,\n                  className: \"contact-form\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"form-row\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"form-group\",\n                      children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                        htmlFor: \"name\",\n                        className: \"form-label\",\n                        children: \"Name\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 194,\n                        columnNumber: 53\n                      }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                        type: \"text\",\n                        id: \"name\",\n                        name: \"name\",\n                        value: formData.name,\n                        onChange: handleChange,\n                        className: `form-input ${errors.name ? 'error' : ''}`,\n                        placeholder: \"Your full name\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 195,\n                        columnNumber: 53\n                      }, this), errors.name && /*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"error-message\",\n                        children: errors.name\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 204,\n                        columnNumber: 69\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 193,\n                      columnNumber: 49\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"form-group\",\n                      children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                        htmlFor: \"email\",\n                        className: \"form-label\",\n                        children: \"Email\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 208,\n                        columnNumber: 53\n                      }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                        type: \"email\",\n                        id: \"email\",\n                        name: \"email\",\n                        value: formData.email,\n                        onChange: handleChange,\n                        className: `form-input ${errors.email ? 'error' : ''}`,\n                        placeholder: \"<EMAIL>\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 209,\n                        columnNumber: 53\n                      }, this), errors.email && /*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"error-message\",\n                        children: errors.email\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 218,\n                        columnNumber: 70\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 207,\n                      columnNumber: 49\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 192,\n                    columnNumber: 45\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"form-group\",\n                    children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                      htmlFor: \"subject\",\n                      className: \"form-label\",\n                      children: \"Subject\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 223,\n                      columnNumber: 49\n                    }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                      type: \"text\",\n                      id: \"subject\",\n                      name: \"subject\",\n                      value: formData.subject,\n                      onChange: handleChange,\n                      className: `form-input ${errors.subject ? 'error' : ''}`,\n                      placeholder: \"What's this about?\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 224,\n                      columnNumber: 49\n                    }, this), errors.subject && /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"error-message\",\n                      children: errors.subject\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 233,\n                      columnNumber: 68\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 222,\n                    columnNumber: 45\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"form-group\",\n                    children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                      htmlFor: \"message\",\n                      className: \"form-label\",\n                      children: \"Message\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 237,\n                      columnNumber: 49\n                    }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n                      id: \"message\",\n                      name: \"message\",\n                      value: formData.message,\n                      onChange: handleChange,\n                      className: `form-input form-textarea ${errors.message ? 'error' : ''}`,\n                      placeholder: \"Tell us more about your inquiry...\",\n                      rows: \"5\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 238,\n                      columnNumber: 49\n                    }, this), errors.message && /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"error-message\",\n                      children: errors.message\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 247,\n                      columnNumber: 68\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 236,\n                    columnNumber: 45\n                  }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                    type: \"submit\",\n                    className: `submit-btn ${isLoading ? 'loading' : ''}`,\n                    disabled: isLoading,\n                    children: isLoading ? /*#__PURE__*/_jsxDEV(_Fragment, {\n                      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"loading-spinner\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 257,\n                        columnNumber: 57\n                      }, this), \"Sending...\"]\n                    }, void 0, true) : /*#__PURE__*/_jsxDEV(_Fragment, {\n                      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"btn-icon\",\n                        children: \"\\uD83D\\uDCE4\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 262,\n                        columnNumber: 57\n                      }, this), \"Send Message\"]\n                    }, void 0, true)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 250,\n                    columnNumber: 45\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 191,\n                  columnNumber: 41\n                }, this)]\n              }, void 0, true)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 175,\n              columnNumber: 29\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 174,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 151,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 150,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 149,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n      className: \"contact-faq\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"container\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"section-header\",\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            className: \"section-title fade-in-up\",\n            children: \"Frequently Asked Questions\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 280,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"section-subtitle fade-in-up\",\n            children: \"Quick answers to common questions\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 281,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 279,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"faq-grid\",\n          children: faqs.map((faq, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"faq-item glass-card fade-in-up\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"faq-question\",\n              children: faq.question\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 289,\n              columnNumber: 33\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"faq-answer\",\n              children: faq.answer\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 290,\n              columnNumber: 33\n            }, this)]\n          }, index, true, {\n            fileName: _jsxFileName,\n            lineNumber: 288,\n            columnNumber: 29\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 286,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 278,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 277,\n      columnNumber: 13\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 128,\n    columnNumber: 9\n  }, this);\n}\n_s(Contact, \"b8jlkpxr2Q+gFfX9gk1AkqubbtA=\");\n_c = Contact;\nexport default Contact;\nvar _c;\n$RefreshReg$(_c, \"Contact\");", "map": {"version": 3, "names": ["useState", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "Contact", "_s", "formData", "setFormData", "name", "email", "subject", "message", "errors", "setErrors", "isLoading", "setIsLoading", "isSubmitted", "setIsSubmitted", "handleChange", "e", "value", "target", "prev", "validateForm", "newErrors", "trim", "test", "length", "Object", "keys", "handleSubmit", "preventDefault", "Promise", "resolve", "setTimeout", "console", "log", "error", "alert", "contactInfo", "icon", "title", "info", "description", "faqs", "question", "answer", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "map", "item", "index", "onClick", "onSubmit", "htmlFor", "type", "id", "onChange", "placeholder", "rows", "disabled", "faq", "_c", "$RefreshReg$"], "sources": ["D:/Expense Tracker App (MERN )/expensetracker/src/pages/Contact.js"], "sourcesContent": ["import { useState } from 'react';\r\nimport './Contact.css';\r\n\r\nfunction Contact() {\r\n    const [formData, setFormData] = useState({\r\n        name: '',\r\n        email: '',\r\n        subject: '',\r\n        message: ''\r\n    });\r\n\r\n    const [errors, setErrors] = useState({});\r\n    const [isLoading, setIsLoading] = useState(false);\r\n    const [isSubmitted, setIsSubmitted] = useState(false);\r\n\r\n    const handleChange = (e) => {\r\n        const { name, value } = e.target;\r\n        setFormData(prev => ({\r\n            ...prev,\r\n            [name]: value\r\n        }));\r\n        // Clear error when user starts typing\r\n        if (errors[name]) {\r\n            setErrors(prev => ({\r\n                ...prev,\r\n                [name]: ''\r\n            }));\r\n        }\r\n    };\r\n\r\n    const validateForm = () => {\r\n        const newErrors = {};\r\n\r\n        if (!formData.name.trim()) {\r\n            newErrors.name = 'Name is required';\r\n        }\r\n\r\n        if (!formData.email) {\r\n            newErrors.email = 'Email is required';\r\n        } else if (!/\\S+@\\S+\\.\\S+/.test(formData.email)) {\r\n            newErrors.email = 'Email is invalid';\r\n        }\r\n\r\n        if (!formData.subject.trim()) {\r\n            newErrors.subject = 'Subject is required';\r\n        }\r\n\r\n        if (!formData.message.trim()) {\r\n            newErrors.message = 'Message is required';\r\n        } else if (formData.message.trim().length < 10) {\r\n            newErrors.message = 'Message must be at least 10 characters';\r\n        }\r\n\r\n        setErrors(newErrors);\r\n        return Object.keys(newErrors).length === 0;\r\n    };\r\n\r\n    const handleSubmit = async (e) => {\r\n        e.preventDefault();\r\n\r\n        if (!validateForm()) {\r\n            return;\r\n        }\r\n\r\n        setIsLoading(true);\r\n\r\n        try {\r\n            // Simulate API call\r\n            await new Promise(resolve => setTimeout(resolve, 2000));\r\n            console.log('Contact form data:', formData);\r\n            setIsSubmitted(true);\r\n            setFormData({ name: '', email: '', subject: '', message: '' });\r\n        } catch (error) {\r\n            console.error('Contact form error:', error);\r\n            alert('Failed to send message. Please try again.');\r\n        } finally {\r\n            setIsLoading(false);\r\n        }\r\n    };\r\n\r\n    const contactInfo = [\r\n        {\r\n            icon: \"📧\",\r\n            title: \"Email Us\",\r\n            info: \"<EMAIL>\",\r\n            description: \"Get in touch for support or general inquiries\"\r\n        },\r\n        {\r\n            icon: \"📞\",\r\n            title: \"Call Us\",\r\n            info: \"+1 (555) 123-4567\",\r\n            description: \"Mon-Fri 9AM-6PM EST\"\r\n        },\r\n        {\r\n            icon: \"📍\",\r\n            title: \"Visit Us\",\r\n            info: \"123 Tech Street, Silicon Valley, CA 94000\",\r\n            description: \"Our headquarters - visitors welcome!\"\r\n        },\r\n        {\r\n            icon: \"💬\",\r\n            title: \"Live Chat\",\r\n            info: \"Available 24/7\",\r\n            description: \"Instant support through our website\"\r\n        }\r\n    ];\r\n\r\n    const faqs = [\r\n        {\r\n            question: \"How secure is my financial data?\",\r\n            answer: \"We use bank-level encryption and never store your banking credentials. All data is encrypted both in transit and at rest.\"\r\n        },\r\n        {\r\n            question: \"Can I export my expense data?\",\r\n            answer: \"Yes! You can export your data in CSV, PDF, or Excel format anytime from your dashboard.\"\r\n        },\r\n        {\r\n            question: \"Is there a mobile app?\",\r\n            answer: \"Our web app is fully responsive and works great on mobile. Native iOS and Android apps are coming soon!\"\r\n        },\r\n        {\r\n            question: \"What payment methods do you accept?\",\r\n            answer: \"We accept all major credit cards, PayPal, and bank transfers. All payments are processed securely.\"\r\n        }\r\n    ];\r\n\r\n    return (\r\n        <div className=\"contact-container\">\r\n            <div className=\"contact-background\">\r\n                <div className=\"contact-particles\"></div>\r\n            </div>\r\n\r\n            {/* Hero Section */}\r\n            <section className=\"contact-hero\">\r\n                <div className=\"container\">\r\n                    <div className=\"hero-content fade-in-up\">\r\n                        <h1 className=\"contact-title\">\r\n                            Get In <span className=\"gradient-text\">Touch</span>\r\n                        </h1>\r\n                        <p className=\"contact-subtitle\">\r\n                            Have questions? We'd love to hear from you. Send us a message\r\n                            and we'll respond as soon as possible.\r\n                        </p>\r\n                    </div>\r\n                </div>\r\n            </section>\r\n\r\n            {/* Contact Info & Form */}\r\n            <section className=\"contact-main\">\r\n                <div className=\"container\">\r\n                    <div className=\"contact-grid\">\r\n                        {/* Contact Information */}\r\n                        <div className=\"contact-info\">\r\n                            <h2 className=\"info-title fade-in-up\">Contact Information</h2>\r\n                            <p className=\"info-subtitle fade-in-up\">\r\n                                Choose the best way to reach us\r\n                            </p>\r\n\r\n                            <div className=\"contact-methods\">\r\n                                {contactInfo.map((item, index) => (\r\n                                    <div key={index} className=\"contact-method glass-card fade-in-up\">\r\n                                        <div className=\"method-icon\">{item.icon}</div>\r\n                                        <div className=\"method-content\">\r\n                                            <h3 className=\"method-title\">{item.title}</h3>\r\n                                            <p className=\"method-info\">{item.info}</p>\r\n                                            <p className=\"method-description\">{item.description}</p>\r\n                                        </div>\r\n                                    </div>\r\n                                ))}\r\n                            </div>\r\n                        </div>\r\n\r\n                        {/* Contact Form */}\r\n                        <div className=\"contact-form-section\">\r\n                            <div className=\"form-card glass-card\">\r\n                                {isSubmitted ? (\r\n                                    <div className=\"success-message fade-in-up\">\r\n                                        <div className=\"success-icon\">✅</div>\r\n                                        <h3>Message Sent Successfully!</h3>\r\n                                        <p>Thank you for contacting us. We'll get back to you within 24 hours.</p>\r\n                                        <button\r\n                                            onClick={() => setIsSubmitted(false)}\r\n                                            className=\"send-another-btn\"\r\n                                        >\r\n                                            Send Another Message\r\n                                        </button>\r\n                                    </div>\r\n                                ) : (\r\n                                    <>\r\n                                        <h2 className=\"form-title\">Send us a Message</h2>\r\n                                        <form onSubmit={handleSubmit} className=\"contact-form\">\r\n                                            <div className=\"form-row\">\r\n                                                <div className=\"form-group\">\r\n                                                    <label htmlFor=\"name\" className=\"form-label\">Name</label>\r\n                                                    <input\r\n                                                        type=\"text\"\r\n                                                        id=\"name\"\r\n                                                        name=\"name\"\r\n                                                        value={formData.name}\r\n                                                        onChange={handleChange}\r\n                                                        className={`form-input ${errors.name ? 'error' : ''}`}\r\n                                                        placeholder=\"Your full name\"\r\n                                                    />\r\n                                                    {errors.name && <span className=\"error-message\">{errors.name}</span>}\r\n                                                </div>\r\n\r\n                                                <div className=\"form-group\">\r\n                                                    <label htmlFor=\"email\" className=\"form-label\">Email</label>\r\n                                                    <input\r\n                                                        type=\"email\"\r\n                                                        id=\"email\"\r\n                                                        name=\"email\"\r\n                                                        value={formData.email}\r\n                                                        onChange={handleChange}\r\n                                                        className={`form-input ${errors.email ? 'error' : ''}`}\r\n                                                        placeholder=\"<EMAIL>\"\r\n                                                    />\r\n                                                    {errors.email && <span className=\"error-message\">{errors.email}</span>}\r\n                                                </div>\r\n                                            </div>\r\n\r\n                                            <div className=\"form-group\">\r\n                                                <label htmlFor=\"subject\" className=\"form-label\">Subject</label>\r\n                                                <input\r\n                                                    type=\"text\"\r\n                                                    id=\"subject\"\r\n                                                    name=\"subject\"\r\n                                                    value={formData.subject}\r\n                                                    onChange={handleChange}\r\n                                                    className={`form-input ${errors.subject ? 'error' : ''}`}\r\n                                                    placeholder=\"What's this about?\"\r\n                                                />\r\n                                                {errors.subject && <span className=\"error-message\">{errors.subject}</span>}\r\n                                            </div>\r\n\r\n                                            <div className=\"form-group\">\r\n                                                <label htmlFor=\"message\" className=\"form-label\">Message</label>\r\n                                                <textarea\r\n                                                    id=\"message\"\r\n                                                    name=\"message\"\r\n                                                    value={formData.message}\r\n                                                    onChange={handleChange}\r\n                                                    className={`form-input form-textarea ${errors.message ? 'error' : ''}`}\r\n                                                    placeholder=\"Tell us more about your inquiry...\"\r\n                                                    rows=\"5\"\r\n                                                />\r\n                                                {errors.message && <span className=\"error-message\">{errors.message}</span>}\r\n                                            </div>\r\n\r\n                                            <button\r\n                                                type=\"submit\"\r\n                                                className={`submit-btn ${isLoading ? 'loading' : ''}`}\r\n                                                disabled={isLoading}\r\n                                            >\r\n                                                {isLoading ? (\r\n                                                    <>\r\n                                                        <span className=\"loading-spinner\"></span>\r\n                                                        Sending...\r\n                                                    </>\r\n                                                ) : (\r\n                                                    <>\r\n                                                        <span className=\"btn-icon\">📤</span>\r\n                                                        Send Message\r\n                                                    </>\r\n                                                )}\r\n                                            </button>\r\n                                        </form>\r\n                                    </>\r\n                                )}\r\n                            </div>\r\n                        </div>\r\n                    </div>\r\n                </div>\r\n            </section>\r\n\r\n            {/* FAQ Section */}\r\n            <section className=\"contact-faq\">\r\n                <div className=\"container\">\r\n                    <div className=\"section-header\">\r\n                        <h2 className=\"section-title fade-in-up\">Frequently Asked Questions</h2>\r\n                        <p className=\"section-subtitle fade-in-up\">\r\n                            Quick answers to common questions\r\n                        </p>\r\n                    </div>\r\n\r\n                    <div className=\"faq-grid\">\r\n                        {faqs.map((faq, index) => (\r\n                            <div key={index} className=\"faq-item glass-card fade-in-up\">\r\n                                <h3 className=\"faq-question\">{faq.question}</h3>\r\n                                <p className=\"faq-answer\">{faq.answer}</p>\r\n                            </div>\r\n                        ))}\r\n                    </div>\r\n                </div>\r\n            </section>\r\n        </div>\r\n    );\r\n}\r\n\r\nexport default Contact;\r\n"], "mappings": ";;AAAA,SAASA,QAAQ,QAAQ,OAAO;AAChC,OAAO,eAAe;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAEvB,SAASC,OAAOA,CAAA,EAAG;EAAAC,EAAA;EACf,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGR,QAAQ,CAAC;IACrCS,IAAI,EAAE,EAAE;IACRC,KAAK,EAAE,EAAE;IACTC,OAAO,EAAE,EAAE;IACXC,OAAO,EAAE;EACb,CAAC,CAAC;EAEF,MAAM,CAACC,MAAM,EAAEC,SAAS,CAAC,GAAGd,QAAQ,CAAC,CAAC,CAAC,CAAC;EACxC,MAAM,CAACe,SAAS,EAAEC,YAAY,CAAC,GAAGhB,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAACiB,WAAW,EAAEC,cAAc,CAAC,GAAGlB,QAAQ,CAAC,KAAK,CAAC;EAErD,MAAMmB,YAAY,GAAIC,CAAC,IAAK;IACxB,MAAM;MAAEX,IAAI;MAAEY;IAAM,CAAC,GAAGD,CAAC,CAACE,MAAM;IAChCd,WAAW,CAACe,IAAI,KAAK;MACjB,GAAGA,IAAI;MACP,CAACd,IAAI,GAAGY;IACZ,CAAC,CAAC,CAAC;IACH;IACA,IAAIR,MAAM,CAACJ,IAAI,CAAC,EAAE;MACdK,SAAS,CAACS,IAAI,KAAK;QACf,GAAGA,IAAI;QACP,CAACd,IAAI,GAAG;MACZ,CAAC,CAAC,CAAC;IACP;EACJ,CAAC;EAED,MAAMe,YAAY,GAAGA,CAAA,KAAM;IACvB,MAAMC,SAAS,GAAG,CAAC,CAAC;IAEpB,IAAI,CAAClB,QAAQ,CAACE,IAAI,CAACiB,IAAI,CAAC,CAAC,EAAE;MACvBD,SAAS,CAAChB,IAAI,GAAG,kBAAkB;IACvC;IAEA,IAAI,CAACF,QAAQ,CAACG,KAAK,EAAE;MACjBe,SAAS,CAACf,KAAK,GAAG,mBAAmB;IACzC,CAAC,MAAM,IAAI,CAAC,cAAc,CAACiB,IAAI,CAACpB,QAAQ,CAACG,KAAK,CAAC,EAAE;MAC7Ce,SAAS,CAACf,KAAK,GAAG,kBAAkB;IACxC;IAEA,IAAI,CAACH,QAAQ,CAACI,OAAO,CAACe,IAAI,CAAC,CAAC,EAAE;MAC1BD,SAAS,CAACd,OAAO,GAAG,qBAAqB;IAC7C;IAEA,IAAI,CAACJ,QAAQ,CAACK,OAAO,CAACc,IAAI,CAAC,CAAC,EAAE;MAC1BD,SAAS,CAACb,OAAO,GAAG,qBAAqB;IAC7C,CAAC,MAAM,IAAIL,QAAQ,CAACK,OAAO,CAACc,IAAI,CAAC,CAAC,CAACE,MAAM,GAAG,EAAE,EAAE;MAC5CH,SAAS,CAACb,OAAO,GAAG,wCAAwC;IAChE;IAEAE,SAAS,CAACW,SAAS,CAAC;IACpB,OAAOI,MAAM,CAACC,IAAI,CAACL,SAAS,CAAC,CAACG,MAAM,KAAK,CAAC;EAC9C,CAAC;EAED,MAAMG,YAAY,GAAG,MAAOX,CAAC,IAAK;IAC9BA,CAAC,CAACY,cAAc,CAAC,CAAC;IAElB,IAAI,CAACR,YAAY,CAAC,CAAC,EAAE;MACjB;IACJ;IAEAR,YAAY,CAAC,IAAI,CAAC;IAElB,IAAI;MACA;MACA,MAAM,IAAIiB,OAAO,CAACC,OAAO,IAAIC,UAAU,CAACD,OAAO,EAAE,IAAI,CAAC,CAAC;MACvDE,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAE9B,QAAQ,CAAC;MAC3CW,cAAc,CAAC,IAAI,CAAC;MACpBV,WAAW,CAAC;QAAEC,IAAI,EAAE,EAAE;QAAEC,KAAK,EAAE,EAAE;QAAEC,OAAO,EAAE,EAAE;QAAEC,OAAO,EAAE;MAAG,CAAC,CAAC;IAClE,CAAC,CAAC,OAAO0B,KAAK,EAAE;MACZF,OAAO,CAACE,KAAK,CAAC,qBAAqB,EAAEA,KAAK,CAAC;MAC3CC,KAAK,CAAC,2CAA2C,CAAC;IACtD,CAAC,SAAS;MACNvB,YAAY,CAAC,KAAK,CAAC;IACvB;EACJ,CAAC;EAED,MAAMwB,WAAW,GAAG,CAChB;IACIC,IAAI,EAAE,IAAI;IACVC,KAAK,EAAE,UAAU;IACjBC,IAAI,EAAE,4BAA4B;IAClCC,WAAW,EAAE;EACjB,CAAC,EACD;IACIH,IAAI,EAAE,IAAI;IACVC,KAAK,EAAE,SAAS;IAChBC,IAAI,EAAE,mBAAmB;IACzBC,WAAW,EAAE;EACjB,CAAC,EACD;IACIH,IAAI,EAAE,IAAI;IACVC,KAAK,EAAE,UAAU;IACjBC,IAAI,EAAE,2CAA2C;IACjDC,WAAW,EAAE;EACjB,CAAC,EACD;IACIH,IAAI,EAAE,IAAI;IACVC,KAAK,EAAE,WAAW;IAClBC,IAAI,EAAE,gBAAgB;IACtBC,WAAW,EAAE;EACjB,CAAC,CACJ;EAED,MAAMC,IAAI,GAAG,CACT;IACIC,QAAQ,EAAE,kCAAkC;IAC5CC,MAAM,EAAE;EACZ,CAAC,EACD;IACID,QAAQ,EAAE,+BAA+B;IACzCC,MAAM,EAAE;EACZ,CAAC,EACD;IACID,QAAQ,EAAE,wBAAwB;IAClCC,MAAM,EAAE;EACZ,CAAC,EACD;IACID,QAAQ,EAAE,qCAAqC;IAC/CC,MAAM,EAAE;EACZ,CAAC,CACJ;EAED,oBACI7C,OAAA;IAAK8C,SAAS,EAAC,mBAAmB;IAAAC,QAAA,gBAC9B/C,OAAA;MAAK8C,SAAS,EAAC,oBAAoB;MAAAC,QAAA,eAC/B/C,OAAA;QAAK8C,SAAS,EAAC;MAAmB;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACxC,CAAC,eAGNnD,OAAA;MAAS8C,SAAS,EAAC,cAAc;MAAAC,QAAA,eAC7B/C,OAAA;QAAK8C,SAAS,EAAC,WAAW;QAAAC,QAAA,eACtB/C,OAAA;UAAK8C,SAAS,EAAC,yBAAyB;UAAAC,QAAA,gBACpC/C,OAAA;YAAI8C,SAAS,EAAC,eAAe;YAAAC,QAAA,GAAC,SACnB,eAAA/C,OAAA;cAAM8C,SAAS,EAAC,eAAe;cAAAC,QAAA,EAAC;YAAK;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnD,CAAC,eACLnD,OAAA;YAAG8C,SAAS,EAAC,kBAAkB;YAAAC,QAAA,EAAC;UAGhC;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC,eAGVnD,OAAA;MAAS8C,SAAS,EAAC,cAAc;MAAAC,QAAA,eAC7B/C,OAAA;QAAK8C,SAAS,EAAC,WAAW;QAAAC,QAAA,eACtB/C,OAAA;UAAK8C,SAAS,EAAC,cAAc;UAAAC,QAAA,gBAEzB/C,OAAA;YAAK8C,SAAS,EAAC,cAAc;YAAAC,QAAA,gBACzB/C,OAAA;cAAI8C,SAAS,EAAC,uBAAuB;cAAAC,QAAA,EAAC;YAAmB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC9DnD,OAAA;cAAG8C,SAAS,EAAC,0BAA0B;cAAAC,QAAA,EAAC;YAExC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eAEJnD,OAAA;cAAK8C,SAAS,EAAC,iBAAiB;cAAAC,QAAA,EAC3BT,WAAW,CAACc,GAAG,CAAC,CAACC,IAAI,EAAEC,KAAK,kBACzBtD,OAAA;gBAAiB8C,SAAS,EAAC,sCAAsC;gBAAAC,QAAA,gBAC7D/C,OAAA;kBAAK8C,SAAS,EAAC,aAAa;kBAAAC,QAAA,EAAEM,IAAI,CAACd;gBAAI;kBAAAS,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eAC9CnD,OAAA;kBAAK8C,SAAS,EAAC,gBAAgB;kBAAAC,QAAA,gBAC3B/C,OAAA;oBAAI8C,SAAS,EAAC,cAAc;oBAAAC,QAAA,EAAEM,IAAI,CAACb;kBAAK;oBAAAQ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eAC9CnD,OAAA;oBAAG8C,SAAS,EAAC,aAAa;oBAAAC,QAAA,EAAEM,IAAI,CAACZ;kBAAI;oBAAAO,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAC1CnD,OAAA;oBAAG8C,SAAS,EAAC,oBAAoB;oBAAAC,QAAA,EAAEM,IAAI,CAACX;kBAAW;oBAAAM,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACvD,CAAC;cAAA,GANAG,KAAK;gBAAAN,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAOV,CACR;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC,eAGNnD,OAAA;YAAK8C,SAAS,EAAC,sBAAsB;YAAAC,QAAA,eACjC/C,OAAA;cAAK8C,SAAS,EAAC,sBAAsB;cAAAC,QAAA,EAChChC,WAAW,gBACRf,OAAA;gBAAK8C,SAAS,EAAC,4BAA4B;gBAAAC,QAAA,gBACvC/C,OAAA;kBAAK8C,SAAS,EAAC,cAAc;kBAAAC,QAAA,EAAC;gBAAC;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACrCnD,OAAA;kBAAA+C,QAAA,EAAI;gBAA0B;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACnCnD,OAAA;kBAAA+C,QAAA,EAAG;gBAAmE;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC,eAC1EnD,OAAA;kBACIuD,OAAO,EAAEA,CAAA,KAAMvC,cAAc,CAAC,KAAK,CAAE;kBACrC8B,SAAS,EAAC,kBAAkB;kBAAAC,QAAA,EAC/B;gBAED;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACR,CAAC,gBAENnD,OAAA,CAAAE,SAAA;gBAAA6C,QAAA,gBACI/C,OAAA;kBAAI8C,SAAS,EAAC,YAAY;kBAAAC,QAAA,EAAC;gBAAiB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACjDnD,OAAA;kBAAMwD,QAAQ,EAAE3B,YAAa;kBAACiB,SAAS,EAAC,cAAc;kBAAAC,QAAA,gBAClD/C,OAAA;oBAAK8C,SAAS,EAAC,UAAU;oBAAAC,QAAA,gBACrB/C,OAAA;sBAAK8C,SAAS,EAAC,YAAY;sBAAAC,QAAA,gBACvB/C,OAAA;wBAAOyD,OAAO,EAAC,MAAM;wBAACX,SAAS,EAAC,YAAY;wBAAAC,QAAA,EAAC;sBAAI;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAO,CAAC,eACzDnD,OAAA;wBACI0D,IAAI,EAAC,MAAM;wBACXC,EAAE,EAAC,MAAM;wBACTpD,IAAI,EAAC,MAAM;wBACXY,KAAK,EAAEd,QAAQ,CAACE,IAAK;wBACrBqD,QAAQ,EAAE3C,YAAa;wBACvB6B,SAAS,EAAE,cAAcnC,MAAM,CAACJ,IAAI,GAAG,OAAO,GAAG,EAAE,EAAG;wBACtDsD,WAAW,EAAC;sBAAgB;wBAAAb,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC/B,CAAC,EACDxC,MAAM,CAACJ,IAAI,iBAAIP,OAAA;wBAAM8C,SAAS,EAAC,eAAe;wBAAAC,QAAA,EAAEpC,MAAM,CAACJ;sBAAI;wBAAAyC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAO,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACnE,CAAC,eAENnD,OAAA;sBAAK8C,SAAS,EAAC,YAAY;sBAAAC,QAAA,gBACvB/C,OAAA;wBAAOyD,OAAO,EAAC,OAAO;wBAACX,SAAS,EAAC,YAAY;wBAAAC,QAAA,EAAC;sBAAK;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAO,CAAC,eAC3DnD,OAAA;wBACI0D,IAAI,EAAC,OAAO;wBACZC,EAAE,EAAC,OAAO;wBACVpD,IAAI,EAAC,OAAO;wBACZY,KAAK,EAAEd,QAAQ,CAACG,KAAM;wBACtBoD,QAAQ,EAAE3C,YAAa;wBACvB6B,SAAS,EAAE,cAAcnC,MAAM,CAACH,KAAK,GAAG,OAAO,GAAG,EAAE,EAAG;wBACvDqD,WAAW,EAAC;sBAAgB;wBAAAb,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC/B,CAAC,EACDxC,MAAM,CAACH,KAAK,iBAAIR,OAAA;wBAAM8C,SAAS,EAAC,eAAe;wBAAAC,QAAA,EAAEpC,MAAM,CAACH;sBAAK;wBAAAwC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAO,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACrE,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACL,CAAC,eAENnD,OAAA;oBAAK8C,SAAS,EAAC,YAAY;oBAAAC,QAAA,gBACvB/C,OAAA;sBAAOyD,OAAO,EAAC,SAAS;sBAACX,SAAS,EAAC,YAAY;sBAAAC,QAAA,EAAC;oBAAO;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC,eAC/DnD,OAAA;sBACI0D,IAAI,EAAC,MAAM;sBACXC,EAAE,EAAC,SAAS;sBACZpD,IAAI,EAAC,SAAS;sBACdY,KAAK,EAAEd,QAAQ,CAACI,OAAQ;sBACxBmD,QAAQ,EAAE3C,YAAa;sBACvB6B,SAAS,EAAE,cAAcnC,MAAM,CAACF,OAAO,GAAG,OAAO,GAAG,EAAE,EAAG;sBACzDoD,WAAW,EAAC;oBAAoB;sBAAAb,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACnC,CAAC,EACDxC,MAAM,CAACF,OAAO,iBAAIT,OAAA;sBAAM8C,SAAS,EAAC,eAAe;sBAAAC,QAAA,EAAEpC,MAAM,CAACF;oBAAO;sBAAAuC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACzE,CAAC,eAENnD,OAAA;oBAAK8C,SAAS,EAAC,YAAY;oBAAAC,QAAA,gBACvB/C,OAAA;sBAAOyD,OAAO,EAAC,SAAS;sBAACX,SAAS,EAAC,YAAY;sBAAAC,QAAA,EAAC;oBAAO;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC,eAC/DnD,OAAA;sBACI2D,EAAE,EAAC,SAAS;sBACZpD,IAAI,EAAC,SAAS;sBACdY,KAAK,EAAEd,QAAQ,CAACK,OAAQ;sBACxBkD,QAAQ,EAAE3C,YAAa;sBACvB6B,SAAS,EAAE,4BAA4BnC,MAAM,CAACD,OAAO,GAAG,OAAO,GAAG,EAAE,EAAG;sBACvEmD,WAAW,EAAC,oCAAoC;sBAChDC,IAAI,EAAC;oBAAG;sBAAAd,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACX,CAAC,EACDxC,MAAM,CAACD,OAAO,iBAAIV,OAAA;sBAAM8C,SAAS,EAAC,eAAe;sBAAAC,QAAA,EAAEpC,MAAM,CAACD;oBAAO;sBAAAsC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACzE,CAAC,eAENnD,OAAA;oBACI0D,IAAI,EAAC,QAAQ;oBACbZ,SAAS,EAAE,cAAcjC,SAAS,GAAG,SAAS,GAAG,EAAE,EAAG;oBACtDkD,QAAQ,EAAElD,SAAU;oBAAAkC,QAAA,EAEnBlC,SAAS,gBACNb,OAAA,CAAAE,SAAA;sBAAA6C,QAAA,gBACI/C,OAAA;wBAAM8C,SAAS,EAAC;sBAAiB;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAO,CAAC,cAE7C;oBAAA,eAAE,CAAC,gBAEHnD,OAAA,CAAAE,SAAA;sBAAA6C,QAAA,gBACI/C,OAAA;wBAAM8C,SAAS,EAAC,UAAU;wBAAAC,QAAA,EAAC;sBAAE;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC,gBAExC;oBAAA,eAAE;kBACL;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACG,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACP,CAAC;cAAA,eACT;YACL;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC,eAGVnD,OAAA;MAAS8C,SAAS,EAAC,aAAa;MAAAC,QAAA,eAC5B/C,OAAA;QAAK8C,SAAS,EAAC,WAAW;QAAAC,QAAA,gBACtB/C,OAAA;UAAK8C,SAAS,EAAC,gBAAgB;UAAAC,QAAA,gBAC3B/C,OAAA;YAAI8C,SAAS,EAAC,0BAA0B;YAAAC,QAAA,EAAC;UAA0B;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACxEnD,OAAA;YAAG8C,SAAS,EAAC,6BAA6B;YAAAC,QAAA,EAAC;UAE3C;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENnD,OAAA;UAAK8C,SAAS,EAAC,UAAU;UAAAC,QAAA,EACpBJ,IAAI,CAACS,GAAG,CAAC,CAACY,GAAG,EAAEV,KAAK,kBACjBtD,OAAA;YAAiB8C,SAAS,EAAC,gCAAgC;YAAAC,QAAA,gBACvD/C,OAAA;cAAI8C,SAAS,EAAC,cAAc;cAAAC,QAAA,EAAEiB,GAAG,CAACpB;YAAQ;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eAChDnD,OAAA;cAAG8C,SAAS,EAAC,YAAY;cAAAC,QAAA,EAAEiB,GAAG,CAACnB;YAAM;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA,GAFpCG,KAAK;YAAAN,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAGV,CACR;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACT,CAAC;AAEd;AAAC/C,EAAA,CAtSQD,OAAO;AAAA8D,EAAA,GAAP9D,OAAO;AAwShB,eAAeA,OAAO;AAAC,IAAA8D,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}