{"ast": null, "code": "var _jsxFileName = \"D:\\\\Expense Tracker App (MERN )\\\\expensetracker\\\\src\\\\pages\\\\Home.js\";\nimport Hero from \"../components/home/<USER>\";\nimport Features from \"../components/home/<USER>\";\nimport HowItWorks from \"../components/home/<USER>\";\nimport UseCases from \"../components/home/<USER>\";\nimport CTA from \"../components/home/<USER>\";\nimport './Home.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction Home() {\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"home-container\",\n    children: [/*#__PURE__*/_jsxDEV(Hero, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 11,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(Features, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 12,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(HowItWorks, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 13,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(UseCases, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 14,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(CTA, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 15,\n      columnNumber: 13\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 10,\n    columnNumber: 9\n  }, this);\n}\n_c = Home;\nexport default Home;\nvar _c;\n$RefreshReg$(_c, \"Home\");", "map": {"version": 3, "names": ["Hero", "Features", "HowItWorks", "UseCases", "CTA", "jsxDEV", "_jsxDEV", "Home", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["D:/Expense Tracker App (MERN )/expensetracker/src/pages/Home.js"], "sourcesContent": ["import Hero from \"../components/home/<USER>\";\r\nimport Features from \"../components/home/<USER>\";\r\nimport HowItWorks from \"../components/home/<USER>\";\r\nimport UseCases from \"../components/home/<USER>\";\r\nimport CTA from \"../components/home/<USER>\";\r\nimport './Home.css';\r\n\r\nfunction Home() {\r\n    return (\r\n        <div className=\"home-container\">\r\n            <Hero />\r\n            <Features />\r\n            <HowItWorks />\r\n            <UseCases />\r\n            <CTA />\r\n        </div>\r\n    );\r\n}\r\n\r\nexport default Home;"], "mappings": ";AAAA,OAAOA,IAAI,MAAM,yBAAyB;AAC1C,OAAOC,QAAQ,MAAM,6BAA6B;AAClD,OAAOC,UAAU,MAAM,+BAA+B;AACtD,OAAOC,QAAQ,MAAM,6BAA6B;AAClD,OAAOC,GAAG,MAAM,wBAAwB;AACxC,OAAO,YAAY;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEpB,SAASC,IAAIA,CAAA,EAAG;EACZ,oBACID,OAAA;IAAKE,SAAS,EAAC,gBAAgB;IAAAC,QAAA,gBAC3BH,OAAA,CAACN,IAAI;MAAAU,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eACRP,OAAA,CAACL,QAAQ;MAAAS,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eACZP,OAAA,CAACJ,UAAU;MAAAQ,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eACdP,OAAA,CAACH,QAAQ;MAAAO,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eACZP,OAAA,CAACF,GAAG;MAAAM,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACN,CAAC;AAEd;AAACC,EAAA,GAVQP,IAAI;AAYb,eAAeA,IAAI;AAAC,IAAAO,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}