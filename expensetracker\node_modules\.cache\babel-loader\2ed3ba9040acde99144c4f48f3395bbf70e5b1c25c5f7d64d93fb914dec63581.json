{"ast": null, "code": "var _jsxFileName = \"D:\\\\Expense Tracker App (MERN )\\\\expensetracker\\\\src\\\\components\\\\home\\\\UseCases.js\";\nimport './UseCases.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction UseCases() {\n  const useCases = [{\n    icon: \"💼\",\n    title: \"Professionals\",\n    description: \"Track business expenses, manage receipts, and optimize tax deductions effortlessly.\",\n    benefits: [\"Expense reports\", \"Tax optimization\", \"Receipt management\"]\n  }, {\n    icon: \"👨‍🎓\",\n    title: \"Students\",\n    description: \"Monitor spending, stick to budgets, and develop healthy financial habits early.\",\n    benefits: [\"Budget tracking\", \"Spending alerts\", \"Financial education\"]\n  }, {\n    icon: \"🏠\",\n    title: \"Families\",\n    description: \"Coordinate household expenses, plan for goals, and teach kids about money.\",\n    benefits: [\"Shared budgets\", \"Goal planning\", \"Family insights\"]\n  }, {\n    icon: \"🚀\",\n    title: \"Entrepreneurs\",\n    description: \"Separate personal and business expenses, track investments, and plan growth.\",\n    benefits: [\"Business tracking\", \"Investment monitoring\", \"Growth planning\"]\n  }];\n  const testimonials = [{\n    quote: \"This app helped me save 20% more every month! The insights are incredible.\",\n    author: \"<PERSON>\",\n    role: \"Marketing Manager\",\n    avatar: \"👩‍💼\",\n    rating: 5\n  }, {\n    quote: \"Finally, an expense tracker that doesn't feel like work. Love the AI features!\",\n    author: \"Mike Chen\",\n    role: \"Software Developer\",\n    avatar: \"👨‍💻\",\n    rating: 5\n  }, {\n    quote: \"Perfect for our family budget. The kids love tracking their allowances too!\",\n    author: \"Emily Rodriguez\",\n    role: \"Teacher & Mom\",\n    avatar: \"👩‍🏫\",\n    rating: 5\n  }];\n  return /*#__PURE__*/_jsxDEV(\"section\", {\n    className: \"use-cases-section\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"container\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"section-header\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"section-title fade-in-up\",\n          children: [\"Perfect For \", /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"gradient-text\",\n            children: \"Everyone\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 60,\n            columnNumber: 37\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 59,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"section-subtitle fade-in-up\",\n          children: \"Whether you're just starting out or managing complex finances, we've got you covered\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 62,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 58,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"use-cases-grid\",\n        children: useCases.map((useCase, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"use-case-card glass-card fade-in-up\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"use-case-icon\",\n            children: useCase.icon\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 70,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"use-case-title\",\n            children: useCase.title\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 71,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"use-case-description\",\n            children: useCase.description\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 72,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n            className: \"use-case-benefits\",\n            children: useCase.benefits.map((benefit, idx) => /*#__PURE__*/_jsxDEV(\"li\", {\n              children: [\"\\u2713 \", benefit]\n            }, idx, true, {\n              fileName: _jsxFileName,\n              lineNumber: 75,\n              columnNumber: 37\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 73,\n            columnNumber: 29\n          }, this)]\n        }, index, true, {\n          fileName: _jsxFileName,\n          lineNumber: 69,\n          columnNumber: 25\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 67,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"testimonials-section\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"testimonials-title fade-in-up\",\n          children: \"What Our Users Say\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 83,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"testimonials-grid\",\n          children: testimonials.map((testimonial, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"testimonial-card glass-card fade-in-up\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"testimonial-rating\",\n              children: [...Array(testimonial.rating)].map((_, i) => /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"star\",\n                children: \"\\u2B50\"\n              }, i, false, {\n                fileName: _jsxFileName,\n                lineNumber: 89,\n                columnNumber: 41\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 87,\n              columnNumber: 33\n            }, this), /*#__PURE__*/_jsxDEV(\"blockquote\", {\n              className: \"testimonial-quote\",\n              children: [\"\\\"\", testimonial.quote, \"\\\"\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 92,\n              columnNumber: 33\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"testimonial-author\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"author-avatar\",\n                children: testimonial.avatar\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 96,\n                columnNumber: 37\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"author-info\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"author-name\",\n                  children: testimonial.author\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 98,\n                  columnNumber: 41\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"author-role\",\n                  children: testimonial.role\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 99,\n                  columnNumber: 41\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 97,\n                columnNumber: 37\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 95,\n              columnNumber: 33\n            }, this)]\n          }, index, true, {\n            fileName: _jsxFileName,\n            lineNumber: 86,\n            columnNumber: 29\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 84,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 82,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 57,\n      columnNumber: 13\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 56,\n    columnNumber: 9\n  }, this);\n}\n_c = UseCases;\nexport default UseCases;\nvar _c;\n$RefreshReg$(_c, \"UseCases\");", "map": {"version": 3, "names": ["jsxDEV", "_jsxDEV", "UseCases", "useCases", "icon", "title", "description", "benefits", "testimonials", "quote", "author", "role", "avatar", "rating", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "map", "useCase", "index", "benefit", "idx", "testimonial", "Array", "_", "i", "_c", "$RefreshReg$"], "sources": ["D:/Expense Tracker App (MERN )/expensetracker/src/components/home/<USER>"], "sourcesContent": ["import './UseCases.css';\r\n\r\nfunction UseCases() {\r\n    const useCases = [\r\n        {\r\n            icon: \"💼\",\r\n            title: \"Professionals\",\r\n            description: \"Track business expenses, manage receipts, and optimize tax deductions effortlessly.\",\r\n            benefits: [\"Expense reports\", \"Tax optimization\", \"Receipt management\"]\r\n        },\r\n        {\r\n            icon: \"👨‍🎓\",\r\n            title: \"Students\",\r\n            description: \"Monitor spending, stick to budgets, and develop healthy financial habits early.\",\r\n            benefits: [\"Budget tracking\", \"Spending alerts\", \"Financial education\"]\r\n        },\r\n        {\r\n            icon: \"🏠\",\r\n            title: \"Families\",\r\n            description: \"Coordinate household expenses, plan for goals, and teach kids about money.\",\r\n            benefits: [\"Shared budgets\", \"Goal planning\", \"Family insights\"]\r\n        },\r\n        {\r\n            icon: \"🚀\",\r\n            title: \"Entrepreneurs\",\r\n            description: \"Separate personal and business expenses, track investments, and plan growth.\",\r\n            benefits: [\"Business tracking\", \"Investment monitoring\", \"Growth planning\"]\r\n        }\r\n    ];\r\n\r\n    const testimonials = [\r\n        {\r\n            quote: \"This app helped me save 20% more every month! The insights are incredible.\",\r\n            author: \"<PERSON>\",\r\n            role: \"Marketing Manager\",\r\n            avatar: \"👩‍💼\",\r\n            rating: 5\r\n        },\r\n        {\r\n            quote: \"Finally, an expense tracker that doesn't feel like work. Love the AI features!\",\r\n            author: \"<PERSON>\",\r\n            role: \"Software Developer\",\r\n            avatar: \"👨‍💻\",\r\n            rating: 5\r\n        },\r\n        {\r\n            quote: \"Perfect for our family budget. The kids love tracking their allowances too!\",\r\n            author: \"<PERSON>\",\r\n            role: \"Teacher & Mom\",\r\n            avatar: \"👩‍🏫\",\r\n            rating: 5\r\n        }\r\n    ];\r\n\r\n    return (\r\n        <section className=\"use-cases-section\">\r\n            <div className=\"container\">\r\n                <div className=\"section-header\">\r\n                    <h2 className=\"section-title fade-in-up\">\r\n                        Perfect For <span className=\"gradient-text\">Everyone</span>\r\n                    </h2>\r\n                    <p className=\"section-subtitle fade-in-up\">\r\n                        Whether you're just starting out or managing complex finances, we've got you covered\r\n                    </p>\r\n                </div>\r\n\r\n                <div className=\"use-cases-grid\">\r\n                    {useCases.map((useCase, index) => (\r\n                        <div key={index} className=\"use-case-card glass-card fade-in-up\">\r\n                            <div className=\"use-case-icon\">{useCase.icon}</div>\r\n                            <h3 className=\"use-case-title\">{useCase.title}</h3>\r\n                            <p className=\"use-case-description\">{useCase.description}</p>\r\n                            <ul className=\"use-case-benefits\">\r\n                                {useCase.benefits.map((benefit, idx) => (\r\n                                    <li key={idx}>✓ {benefit}</li>\r\n                                ))}\r\n                            </ul>\r\n                        </div>\r\n                    ))}\r\n                </div>\r\n\r\n                <div className=\"testimonials-section\">\r\n                    <h3 className=\"testimonials-title fade-in-up\">What Our Users Say</h3>\r\n                    <div className=\"testimonials-grid\">\r\n                        {testimonials.map((testimonial, index) => (\r\n                            <div key={index} className=\"testimonial-card glass-card fade-in-up\">\r\n                                <div className=\"testimonial-rating\">\r\n                                    {[...Array(testimonial.rating)].map((_, i) => (\r\n                                        <span key={i} className=\"star\">⭐</span>\r\n                                    ))}\r\n                                </div>\r\n                                <blockquote className=\"testimonial-quote\">\r\n                                    \"{testimonial.quote}\"\r\n                                </blockquote>\r\n                                <div className=\"testimonial-author\">\r\n                                    <span className=\"author-avatar\">{testimonial.avatar}</span>\r\n                                    <div className=\"author-info\">\r\n                                        <span className=\"author-name\">{testimonial.author}</span>\r\n                                        <span className=\"author-role\">{testimonial.role}</span>\r\n                                    </div>\r\n                                </div>\r\n                            </div>\r\n                        ))}\r\n                    </div>\r\n                </div>\r\n            </div>\r\n        </section>\r\n    );\r\n}\r\n\r\nexport default UseCases;"], "mappings": ";AAAA,OAAO,gBAAgB;AAAC,SAAAA,MAAA,IAAAC,OAAA;AAExB,SAASC,QAAQA,CAAA,EAAG;EAChB,MAAMC,QAAQ,GAAG,CACb;IACIC,IAAI,EAAE,IAAI;IACVC,KAAK,EAAE,eAAe;IACtBC,WAAW,EAAE,qFAAqF;IAClGC,QAAQ,EAAE,CAAC,iBAAiB,EAAE,kBAAkB,EAAE,oBAAoB;EAC1E,CAAC,EACD;IACIH,IAAI,EAAE,OAAO;IACbC,KAAK,EAAE,UAAU;IACjBC,WAAW,EAAE,iFAAiF;IAC9FC,QAAQ,EAAE,CAAC,iBAAiB,EAAE,iBAAiB,EAAE,qBAAqB;EAC1E,CAAC,EACD;IACIH,IAAI,EAAE,IAAI;IACVC,KAAK,EAAE,UAAU;IACjBC,WAAW,EAAE,4EAA4E;IACzFC,QAAQ,EAAE,CAAC,gBAAgB,EAAE,eAAe,EAAE,iBAAiB;EACnE,CAAC,EACD;IACIH,IAAI,EAAE,IAAI;IACVC,KAAK,EAAE,eAAe;IACtBC,WAAW,EAAE,8EAA8E;IAC3FC,QAAQ,EAAE,CAAC,mBAAmB,EAAE,uBAAuB,EAAE,iBAAiB;EAC9E,CAAC,CACJ;EAED,MAAMC,YAAY,GAAG,CACjB;IACIC,KAAK,EAAE,4EAA4E;IACnFC,MAAM,EAAE,eAAe;IACvBC,IAAI,EAAE,mBAAmB;IACzBC,MAAM,EAAE,OAAO;IACfC,MAAM,EAAE;EACZ,CAAC,EACD;IACIJ,KAAK,EAAE,gFAAgF;IACvFC,MAAM,EAAE,WAAW;IACnBC,IAAI,EAAE,oBAAoB;IAC1BC,MAAM,EAAE,OAAO;IACfC,MAAM,EAAE;EACZ,CAAC,EACD;IACIJ,KAAK,EAAE,6EAA6E;IACpFC,MAAM,EAAE,iBAAiB;IACzBC,IAAI,EAAE,eAAe;IACrBC,MAAM,EAAE,OAAO;IACfC,MAAM,EAAE;EACZ,CAAC,CACJ;EAED,oBACIZ,OAAA;IAASa,SAAS,EAAC,mBAAmB;IAAAC,QAAA,eAClCd,OAAA;MAAKa,SAAS,EAAC,WAAW;MAAAC,QAAA,gBACtBd,OAAA;QAAKa,SAAS,EAAC,gBAAgB;QAAAC,QAAA,gBAC3Bd,OAAA;UAAIa,SAAS,EAAC,0BAA0B;UAAAC,QAAA,GAAC,cACzB,eAAAd,OAAA;YAAMa,SAAS,EAAC,eAAe;YAAAC,QAAA,EAAC;UAAQ;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3D,CAAC,eACLlB,OAAA;UAAGa,SAAS,EAAC,6BAA6B;UAAAC,QAAA,EAAC;QAE3C;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENlB,OAAA;QAAKa,SAAS,EAAC,gBAAgB;QAAAC,QAAA,EAC1BZ,QAAQ,CAACiB,GAAG,CAAC,CAACC,OAAO,EAAEC,KAAK,kBACzBrB,OAAA;UAAiBa,SAAS,EAAC,qCAAqC;UAAAC,QAAA,gBAC5Dd,OAAA;YAAKa,SAAS,EAAC,eAAe;YAAAC,QAAA,EAAEM,OAAO,CAACjB;UAAI;YAAAY,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACnDlB,OAAA;YAAIa,SAAS,EAAC,gBAAgB;YAAAC,QAAA,EAAEM,OAAO,CAAChB;UAAK;YAAAW,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACnDlB,OAAA;YAAGa,SAAS,EAAC,sBAAsB;YAAAC,QAAA,EAAEM,OAAO,CAACf;UAAW;YAAAU,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC7DlB,OAAA;YAAIa,SAAS,EAAC,mBAAmB;YAAAC,QAAA,EAC5BM,OAAO,CAACd,QAAQ,CAACa,GAAG,CAAC,CAACG,OAAO,EAAEC,GAAG,kBAC/BvB,OAAA;cAAAc,QAAA,GAAc,SAAE,EAACQ,OAAO;YAAA,GAAfC,GAAG;cAAAR,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAiB,CAChC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC;QAAA,GARCG,KAAK;UAAAN,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OASV,CACR;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,eAENlB,OAAA;QAAKa,SAAS,EAAC,sBAAsB;QAAAC,QAAA,gBACjCd,OAAA;UAAIa,SAAS,EAAC,+BAA+B;UAAAC,QAAA,EAAC;QAAkB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACrElB,OAAA;UAAKa,SAAS,EAAC,mBAAmB;UAAAC,QAAA,EAC7BP,YAAY,CAACY,GAAG,CAAC,CAACK,WAAW,EAAEH,KAAK,kBACjCrB,OAAA;YAAiBa,SAAS,EAAC,wCAAwC;YAAAC,QAAA,gBAC/Dd,OAAA;cAAKa,SAAS,EAAC,oBAAoB;cAAAC,QAAA,EAC9B,CAAC,GAAGW,KAAK,CAACD,WAAW,CAACZ,MAAM,CAAC,CAAC,CAACO,GAAG,CAAC,CAACO,CAAC,EAAEC,CAAC,kBACrC3B,OAAA;gBAAca,SAAS,EAAC,MAAM;gBAAAC,QAAA,EAAC;cAAC,GAArBa,CAAC;gBAAAZ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAA0B,CACzC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC,eACNlB,OAAA;cAAYa,SAAS,EAAC,mBAAmB;cAAAC,QAAA,GAAC,IACrC,EAACU,WAAW,CAAChB,KAAK,EAAC,IACxB;YAAA;cAAAO,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACblB,OAAA;cAAKa,SAAS,EAAC,oBAAoB;cAAAC,QAAA,gBAC/Bd,OAAA;gBAAMa,SAAS,EAAC,eAAe;gBAAAC,QAAA,EAAEU,WAAW,CAACb;cAAM;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC3DlB,OAAA;gBAAKa,SAAS,EAAC,aAAa;gBAAAC,QAAA,gBACxBd,OAAA;kBAAMa,SAAS,EAAC,aAAa;kBAAAC,QAAA,EAAEU,WAAW,CAACf;gBAAM;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACzDlB,OAAA;kBAAMa,SAAS,EAAC,aAAa;kBAAAC,QAAA,EAAEU,WAAW,CAACd;gBAAI;kBAAAK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC;UAAA,GAfAG,KAAK;YAAAN,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAgBV,CACR;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACD,CAAC;AAElB;AAACU,EAAA,GA1GQ3B,QAAQ;AA4GjB,eAAeA,QAAQ;AAAC,IAAA2B,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}