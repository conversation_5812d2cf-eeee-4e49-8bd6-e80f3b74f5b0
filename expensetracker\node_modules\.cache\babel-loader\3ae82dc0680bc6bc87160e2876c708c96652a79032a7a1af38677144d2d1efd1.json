{"ast": null, "code": "var _jsxFileName = \"D:\\\\Expense Tracker App (MERN )\\\\expensetracker\\\\src\\\\App.js\",\n  _s = $RefreshSig$();\nimport { BrowserRouter as Router, Routes, Route, useLocation } from \"react-router-dom\";\nimport { useEffect, useState } from \"react\";\nimport './App.css';\nimport Navbar from \"./components/Navbar\";\nimport Loader from \"./components/Loader\";\nimport Home from \"./pages/Home\";\nimport About from \"./pages/About\";\nimport Contact from \"./pages/Contact\";\nimport Login from \"./pages/Login\";\nimport Signup from \"./pages/Signup\";\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nfunction AppContent() {\n  _s();\n  const location = useLocation();\n  const [loading, setLoading] = useState(false);\n  useEffect(() => {\n    setLoading(true);\n    const timer = setTimeout(() => {\n      setLoading(false);\n    }, 500); // half a second delay\n\n    return () => clearTimeout(timer);\n  }, [location]);\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(Loader, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 27,\n      columnNumber: 12\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [/*#__PURE__*/_jsxDEV(Navbar, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 31,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Routes, {\n      children: [/*#__PURE__*/_jsxDEV(Route, {\n        path: \"/\",\n        element: /*#__PURE__*/_jsxDEV(Home, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 33,\n          columnNumber: 34\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 33,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Route, {\n        path: \"/about\",\n        element: /*#__PURE__*/_jsxDEV(About, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 34,\n          columnNumber: 39\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 34,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Route, {\n        path: \"/contact\",\n        element: /*#__PURE__*/_jsxDEV(Contact, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 35,\n          columnNumber: 41\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 35,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Route, {\n        path: \"/login\",\n        element: /*#__PURE__*/_jsxDEV(Login, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 36,\n          columnNumber: 39\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 36,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Route, {\n        path: \"/signup\",\n        element: /*#__PURE__*/_jsxDEV(Signup, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 37,\n          columnNumber: 40\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 37,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 32,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true);\n}\n_s(AppContent, \"12V4DhK/+FphMBmcfsoKCCzma6g=\", false, function () {\n  return [useLocation];\n});\n_c = AppContent;\nfunction App() {\n  return /*#__PURE__*/_jsxDEV(Router, {\n    children: /*#__PURE__*/_jsxDEV(AppContent, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 46,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 45,\n    columnNumber: 5\n  }, this);\n}\n_c2 = App;\nexport default App;\nvar _c, _c2;\n$RefreshReg$(_c, \"AppContent\");\n$RefreshReg$(_c2, \"App\");", "map": {"version": 3, "names": ["<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Router", "Routes", "Route", "useLocation", "useEffect", "useState", "<PERSON><PERSON><PERSON>", "Loader", "Home", "About", "Contact", "<PERSON><PERSON>", "Signup", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "A<PERSON><PERSON><PERSON>nt", "_s", "location", "loading", "setLoading", "timer", "setTimeout", "clearTimeout", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "children", "path", "element", "_c", "App", "_c2", "$RefreshReg$"], "sources": ["D:/Expense Tracker App (MERN )/expensetracker/src/App.js"], "sourcesContent": ["import { BrowserRouter as Router, Routes, Route, useLocation } from \"react-router-dom\";\nimport { useEffect, useState } from \"react\";\nimport './App.css';\nimport Navbar from \"./components/Navbar\";\nimport Loader from \"./components/Loader\";\nimport Home from \"./pages/Home\";\nimport About from \"./pages/About\";\nimport Contact from \"./pages/Contact\";\nimport Login from \"./pages/Login\";\nimport Signup from \"./pages/Signup\";\n\nfunction AppContent() {\n  const location = useLocation();\n  const [loading, setLoading] = useState(false);\n\n  useEffect(() => {\n    setLoading(true);\n\n    const timer = setTimeout(() => {\n      setLoading(false);\n    }, 500); // half a second delay\n\n    return () => clearTimeout(timer);\n  }, [location]);\n\n  if (loading) {\n    return <Loader />;\n  }\n  return (\n    <>\n      <Navbar />\n      <Routes>\n        <Route path=\"/\" element={<Home />} />\n        <Route path=\"/about\" element={<About />} />\n        <Route path=\"/contact\" element={<Contact />} />\n        <Route path=\"/login\" element={<Login />} />\n        <Route path=\"/signup\" element={<Signup />} />\n      </Routes>\n    </>\n  );\n}\n\nfunction App() {\n  return (\n    <Router>\n      <AppContent />\n    </Router>\n  );\n}\n\nexport default App;\n"], "mappings": ";;AAAA,SAASA,aAAa,IAAIC,MAAM,EAAEC,MAAM,EAAEC,KAAK,EAAEC,WAAW,QAAQ,kBAAkB;AACtF,SAASC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAC3C,OAAO,WAAW;AAClB,OAAOC,MAAM,MAAM,qBAAqB;AACxC,OAAOC,MAAM,MAAM,qBAAqB;AACxC,OAAOC,IAAI,MAAM,cAAc;AAC/B,OAAOC,KAAK,MAAM,eAAe;AACjC,OAAOC,OAAO,MAAM,iBAAiB;AACrC,OAAOC,KAAK,MAAM,eAAe;AACjC,OAAOC,MAAM,MAAM,gBAAgB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAEpC,SAASC,UAAUA,CAAA,EAAG;EAAAC,EAAA;EACpB,MAAMC,QAAQ,GAAGhB,WAAW,CAAC,CAAC;EAC9B,MAAM,CAACiB,OAAO,EAAEC,UAAU,CAAC,GAAGhB,QAAQ,CAAC,KAAK,CAAC;EAE7CD,SAAS,CAAC,MAAM;IACdiB,UAAU,CAAC,IAAI,CAAC;IAEhB,MAAMC,KAAK,GAAGC,UAAU,CAAC,MAAM;MAC7BF,UAAU,CAAC,KAAK,CAAC;IACnB,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC;;IAET,OAAO,MAAMG,YAAY,CAACF,KAAK,CAAC;EAClC,CAAC,EAAE,CAACH,QAAQ,CAAC,CAAC;EAEd,IAAIC,OAAO,EAAE;IACX,oBAAON,OAAA,CAACP,MAAM;MAAAkB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EACnB;EACA,oBACEd,OAAA,CAAAE,SAAA;IAAAa,QAAA,gBACEf,OAAA,CAACR,MAAM;MAAAmB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eACVd,OAAA,CAACb,MAAM;MAAA4B,QAAA,gBACLf,OAAA,CAACZ,KAAK;QAAC4B,IAAI,EAAC,GAAG;QAACC,OAAO,eAAEjB,OAAA,CAACN,IAAI;UAAAiB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAE;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACrCd,OAAA,CAACZ,KAAK;QAAC4B,IAAI,EAAC,QAAQ;QAACC,OAAO,eAAEjB,OAAA,CAACL,KAAK;UAAAgB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAE;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAC3Cd,OAAA,CAACZ,KAAK;QAAC4B,IAAI,EAAC,UAAU;QAACC,OAAO,eAAEjB,OAAA,CAACJ,OAAO;UAAAe,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAE;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAC/Cd,OAAA,CAACZ,KAAK;QAAC4B,IAAI,EAAC,QAAQ;QAACC,OAAO,eAAEjB,OAAA,CAACH,KAAK;UAAAc,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAE;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAC3Cd,OAAA,CAACZ,KAAK;QAAC4B,IAAI,EAAC,SAAS;QAACC,OAAO,eAAEjB,OAAA,CAACF,MAAM;UAAAa,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAE;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACvC,CAAC;EAAA,eACT,CAAC;AAEP;AAACV,EAAA,CA7BQD,UAAU;EAAA,QACAd,WAAW;AAAA;AAAA6B,EAAA,GADrBf,UAAU;AA+BnB,SAASgB,GAAGA,CAAA,EAAG;EACb,oBACEnB,OAAA,CAACd,MAAM;IAAA6B,QAAA,eACLf,OAAA,CAACG,UAAU;MAAAQ,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACR,CAAC;AAEb;AAACM,GAAA,GANQD,GAAG;AAQZ,eAAeA,GAAG;AAAC,IAAAD,EAAA,EAAAE,GAAA;AAAAC,YAAA,CAAAH,EAAA;AAAAG,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}