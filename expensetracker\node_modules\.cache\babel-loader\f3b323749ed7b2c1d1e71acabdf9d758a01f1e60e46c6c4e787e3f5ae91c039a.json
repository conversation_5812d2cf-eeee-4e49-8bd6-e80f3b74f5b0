{"ast": null, "code": "var _jsxFileName = \"D:\\\\Expense Tracker App (MERN )\\\\expensetracker\\\\src\\\\components\\\\home\\\\HowItWorks.js\";\nimport './HowItWorks.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction HowItWorks() {\n  const steps = [{\n    number: \"01\",\n    title: \"Create Your Account\",\n    description: \"Sign up in seconds with your email or social login. No credit card required for the free trial.\",\n    icon: \"👤\",\n    color: \"var(--accent-primary)\"\n  }, {\n    number: \"02\",\n    title: \"Add Your Expenses\",\n    description: \"Log expenses instantly with our smart categorization, receipt scanning, and voice input features.\",\n    icon: \"💳\",\n    color: \"var(--accent-tertiary)\"\n  }, {\n    number: \"03\",\n    title: \"Get Smart Insights\",\n    description: \"View beautiful analytics, set budgets, and receive AI-powered recommendations for better financial health.\",\n    icon: \"📈\",\n    color: \"var(--accent-secondary)\"\n  }];\n  return /*#__PURE__*/_jsxDEV(\"section\", {\n    className: \"how-it-works-section\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"container\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"section-header\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"section-title fade-in-up\",\n          children: [\"How It \", /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"gradient-text\",\n            children: \"Works\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 33,\n            columnNumber: 32\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 32,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"section-subtitle fade-in-up\",\n          children: \"Get started with expense tracking in just 3 simple steps\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 35,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 31,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"steps-container\",\n        children: steps.map((step, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"step-card fade-in-up\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"step-number\",\n            style: {\n              color: step.color\n            },\n            children: step.number\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 43,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"step-icon\",\n            children: step.icon\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 46,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"step-content\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"step-title\",\n              children: step.title\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 50,\n              columnNumber: 33\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"step-description\",\n              children: step.description\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 51,\n              columnNumber: 33\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 49,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"step-connector\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 53,\n            columnNumber: 29\n          }, this)]\n        }, index, true, {\n          fileName: _jsxFileName,\n          lineNumber: 42,\n          columnNumber: 25\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 40,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"demo-section fade-in-up\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"demo-card glass-card\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"demo-title\",\n            children: \"Ready to see it in action?\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 60,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"demo-description\",\n            children: \"Watch our 2-minute demo to see how easy expense tracking can be\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 61,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"demo-btn gradient-btn\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"btn-icon\",\n              children: \"\\u25B6\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 65,\n              columnNumber: 29\n            }, this), \"Watch Demo\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 64,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 59,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 58,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 30,\n      columnNumber: 13\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 29,\n    columnNumber: 9\n  }, this);\n}\n_c = HowItWorks;\nexport default HowItWorks;\nvar _c;\n$RefreshReg$(_c, \"HowItWorks\");", "map": {"version": 3, "names": ["jsxDEV", "_jsxDEV", "HowItWorks", "steps", "number", "title", "description", "icon", "color", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "map", "step", "index", "style", "_c", "$RefreshReg$"], "sources": ["D:/Expense Tracker App (MERN )/expensetracker/src/components/home/<USER>"], "sourcesContent": ["import './HowItWorks.css';\r\n\r\nfunction HowItWorks() {\r\n    const steps = [\r\n        {\r\n            number: \"01\",\r\n            title: \"Create Your Account\",\r\n            description: \"Sign up in seconds with your email or social login. No credit card required for the free trial.\",\r\n            icon: \"👤\",\r\n            color: \"var(--accent-primary)\"\r\n        },\r\n        {\r\n            number: \"02\",\r\n            title: \"Add Your Expenses\",\r\n            description: \"Log expenses instantly with our smart categorization, receipt scanning, and voice input features.\",\r\n            icon: \"💳\",\r\n            color: \"var(--accent-tertiary)\"\r\n        },\r\n        {\r\n            number: \"03\",\r\n            title: \"Get Smart Insights\",\r\n            description: \"View beautiful analytics, set budgets, and receive AI-powered recommendations for better financial health.\",\r\n            icon: \"📈\",\r\n            color: \"var(--accent-secondary)\"\r\n        }\r\n    ];\r\n\r\n    return (\r\n        <section className=\"how-it-works-section\">\r\n            <div className=\"container\">\r\n                <div className=\"section-header\">\r\n                    <h2 className=\"section-title fade-in-up\">\r\n                        How It <span className=\"gradient-text\">Works</span>\r\n                    </h2>\r\n                    <p className=\"section-subtitle fade-in-up\">\r\n                        Get started with expense tracking in just 3 simple steps\r\n                    </p>\r\n                </div>\r\n\r\n                <div className=\"steps-container\">\r\n                    {steps.map((step, index) => (\r\n                        <div key={index} className=\"step-card fade-in-up\">\r\n                            <div className=\"step-number\" style={{ color: step.color }}>\r\n                                {step.number}\r\n                            </div>\r\n                            <div className=\"step-icon\">\r\n                                {step.icon}\r\n                            </div>\r\n                            <div className=\"step-content\">\r\n                                <h3 className=\"step-title\">{step.title}</h3>\r\n                                <p className=\"step-description\">{step.description}</p>\r\n                            </div>\r\n                            <div className=\"step-connector\"></div>\r\n                        </div>\r\n                    ))}\r\n                </div>\r\n\r\n                <div className=\"demo-section fade-in-up\">\r\n                    <div className=\"demo-card glass-card\">\r\n                        <h3 className=\"demo-title\">Ready to see it in action?</h3>\r\n                        <p className=\"demo-description\">\r\n                            Watch our 2-minute demo to see how easy expense tracking can be\r\n                        </p>\r\n                        <button className=\"demo-btn gradient-btn\">\r\n                            <span className=\"btn-icon\">▶</span>\r\n                            Watch Demo\r\n                        </button>\r\n                    </div>\r\n                </div>\r\n            </div>\r\n        </section>\r\n    );\r\n}\r\n\r\nexport default HowItWorks;\r\n"], "mappings": ";AAAA,OAAO,kBAAkB;AAAC,SAAAA,MAAA,IAAAC,OAAA;AAE1B,SAASC,UAAUA,CAAA,EAAG;EAClB,MAAMC,KAAK,GAAG,CACV;IACIC,MAAM,EAAE,IAAI;IACZC,KAAK,EAAE,qBAAqB;IAC5BC,WAAW,EAAE,iGAAiG;IAC9GC,IAAI,EAAE,IAAI;IACVC,KAAK,EAAE;EACX,CAAC,EACD;IACIJ,MAAM,EAAE,IAAI;IACZC,KAAK,EAAE,mBAAmB;IAC1BC,WAAW,EAAE,mGAAmG;IAChHC,IAAI,EAAE,IAAI;IACVC,KAAK,EAAE;EACX,CAAC,EACD;IACIJ,MAAM,EAAE,IAAI;IACZC,KAAK,EAAE,oBAAoB;IAC3BC,WAAW,EAAE,4GAA4G;IACzHC,IAAI,EAAE,IAAI;IACVC,KAAK,EAAE;EACX,CAAC,CACJ;EAED,oBACIP,OAAA;IAASQ,SAAS,EAAC,sBAAsB;IAAAC,QAAA,eACrCT,OAAA;MAAKQ,SAAS,EAAC,WAAW;MAAAC,QAAA,gBACtBT,OAAA;QAAKQ,SAAS,EAAC,gBAAgB;QAAAC,QAAA,gBAC3BT,OAAA;UAAIQ,SAAS,EAAC,0BAA0B;UAAAC,QAAA,GAAC,SAC9B,eAAAT,OAAA;YAAMQ,SAAS,EAAC,eAAe;YAAAC,QAAA,EAAC;UAAK;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnD,CAAC,eACLb,OAAA;UAAGQ,SAAS,EAAC,6BAA6B;UAAAC,QAAA,EAAC;QAE3C;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENb,OAAA;QAAKQ,SAAS,EAAC,iBAAiB;QAAAC,QAAA,EAC3BP,KAAK,CAACY,GAAG,CAAC,CAACC,IAAI,EAAEC,KAAK,kBACnBhB,OAAA;UAAiBQ,SAAS,EAAC,sBAAsB;UAAAC,QAAA,gBAC7CT,OAAA;YAAKQ,SAAS,EAAC,aAAa;YAACS,KAAK,EAAE;cAAEV,KAAK,EAAEQ,IAAI,CAACR;YAAM,CAAE;YAAAE,QAAA,EACrDM,IAAI,CAACZ;UAAM;YAAAO,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACX,CAAC,eACNb,OAAA;YAAKQ,SAAS,EAAC,WAAW;YAAAC,QAAA,EACrBM,IAAI,CAACT;UAAI;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eACNb,OAAA;YAAKQ,SAAS,EAAC,cAAc;YAAAC,QAAA,gBACzBT,OAAA;cAAIQ,SAAS,EAAC,YAAY;cAAAC,QAAA,EAAEM,IAAI,CAACX;YAAK;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eAC5Cb,OAAA;cAAGQ,SAAS,EAAC,kBAAkB;cAAAC,QAAA,EAAEM,IAAI,CAACV;YAAW;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrD,CAAC,eACNb,OAAA;YAAKQ,SAAS,EAAC;UAAgB;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA,GAXhCG,KAAK;UAAAN,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAYV,CACR;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,eAENb,OAAA;QAAKQ,SAAS,EAAC,yBAAyB;QAAAC,QAAA,eACpCT,OAAA;UAAKQ,SAAS,EAAC,sBAAsB;UAAAC,QAAA,gBACjCT,OAAA;YAAIQ,SAAS,EAAC,YAAY;YAAAC,QAAA,EAAC;UAA0B;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC1Db,OAAA;YAAGQ,SAAS,EAAC,kBAAkB;YAAAC,QAAA,EAAC;UAEhC;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eACJb,OAAA;YAAQQ,SAAS,EAAC,uBAAuB;YAAAC,QAAA,gBACrCT,OAAA;cAAMQ,SAAS,EAAC,UAAU;cAAAC,QAAA,EAAC;YAAC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,cAEvC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACD,CAAC;AAElB;AAACK,EAAA,GAtEQjB,UAAU;AAwEnB,eAAeA,UAAU;AAAC,IAAAiB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}