{"ast": null, "code": "var _jsxFileName = \"D:\\\\Expense Tracker App (MERN )\\\\expensetracker\\\\src\\\\components\\\\home\\\\Hero.js\";\nimport './Hero.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction Hero() {\n  return /*#__PURE__*/_jsxDEV(\"section\", {\n    className: \"hero-section\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"hero-background\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"hero-particles\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 7,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 6,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"container\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"hero-content\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"hero-badge fade-in-up\",\n          children: /*#__PURE__*/_jsxDEV(\"span\", {\n            children: \"\\u2728 New Feature: AI-Powered Insights\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 12,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 11,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"h1\", {\n          className: \"hero-title fade-in-up\",\n          children: [\"Track Every \", /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"gradient-text\",\n            children: \"Rupee\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 15,\n            columnNumber: 37\n          }, this), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 16,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"gradient-text\",\n            children: \"Effortlessly\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 17,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 14,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"hero-subtitle fade-in-up\",\n          children: \"Transform your financial habits with our intelligent expense tracker. Smart analytics, beautiful insights, and complete privacy.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 19,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"hero-buttons fade-in-up\",\n          children: [/*#__PURE__*/_jsxDEV(\"a\", {\n            href: \"/signup\",\n            className: \"gradient-btn primary-btn\",\n            children: [\"Start Free Trial\", /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"btn-icon\",\n              children: \"\\u2192\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 26,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 24,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n            href: \"#features\",\n            className: \"glass-btn\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"btn-icon\",\n              children: \"\\u25B6\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 29,\n              columnNumber: 29\n            }, this), \"Watch Demo\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 28,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 23,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"hero-stats fade-in-up\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"stat-item\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"stat-number\",\n              children: \"10K+\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 35,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"stat-label\",\n              children: \"Active Users\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 36,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 34,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"stat-item\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"stat-number\",\n              children: \"\\u20B950L+\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 39,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"stat-label\",\n              children: \"Money Tracked\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 40,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 38,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"stat-item\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"stat-number\",\n              children: \"4.9\\u2605\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 43,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"stat-label\",\n              children: \"User Rating\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 44,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 42,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 33,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 10,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 9,\n      columnNumber: 13\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 5,\n    columnNumber: 9\n  }, this);\n}\n_c = Hero;\nexport default Hero;\nvar _c;\n$RefreshReg$(_c, \"Hero\");", "map": {"version": 3, "names": ["jsxDEV", "_jsxDEV", "Hero", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "href", "_c", "$RefreshReg$"], "sources": ["D:/Expense Tracker App (MERN )/expensetracker/src/components/home/<USER>"], "sourcesContent": ["import './Hero.css';\r\n\r\nfunction Hero() {\r\n    return (\r\n        <section className=\"hero-section\">\r\n            <div className=\"hero-background\">\r\n                <div className=\"hero-particles\"></div>\r\n            </div>\r\n            <div className=\"container\">\r\n                <div className=\"hero-content\">\r\n                    <div className=\"hero-badge fade-in-up\">\r\n                        <span>✨ New Feature: AI-Powered Insights</span>\r\n                    </div>\r\n                    <h1 className=\"hero-title fade-in-up\">\r\n                        Track Every <span className=\"gradient-text\">Rupee</span>\r\n                        <br />\r\n                        <span className=\"gradient-text\">Effortlessly</span>\r\n                    </h1>\r\n                    <p className=\"hero-subtitle fade-in-up\">\r\n                        Transform your financial habits with our intelligent expense tracker.\r\n                        Smart analytics, beautiful insights, and complete privacy.\r\n                    </p>\r\n                    <div className=\"hero-buttons fade-in-up\">\r\n                        <a href=\"/signup\" className=\"gradient-btn primary-btn\">\r\n                            Start Free Trial\r\n                            <span className=\"btn-icon\">→</span>\r\n                        </a>\r\n                        <a href=\"#features\" className=\"glass-btn\">\r\n                            <span className=\"btn-icon\">▶</span>\r\n                            Watch Demo\r\n                        </a>\r\n                    </div>\r\n                    <div className=\"hero-stats fade-in-up\">\r\n                        <div className=\"stat-item\">\r\n                            <span className=\"stat-number\">10K+</span>\r\n                            <span className=\"stat-label\">Active Users</span>\r\n                        </div>\r\n                        <div className=\"stat-item\">\r\n                            <span className=\"stat-number\">₹50L+</span>\r\n                            <span className=\"stat-label\">Money Tracked</span>\r\n                        </div>\r\n                        <div className=\"stat-item\">\r\n                            <span className=\"stat-number\">4.9★</span>\r\n                            <span className=\"stat-label\">User Rating</span>\r\n                        </div>\r\n                    </div>\r\n                </div>\r\n            </div>\r\n        </section>\r\n    );\r\n}\r\n\r\nexport default Hero;"], "mappings": ";AAAA,OAAO,YAAY;AAAC,SAAAA,MAAA,IAAAC,OAAA;AAEpB,SAASC,IAAIA,CAAA,EAAG;EACZ,oBACID,OAAA;IAASE,SAAS,EAAC,cAAc;IAAAC,QAAA,gBAC7BH,OAAA;MAAKE,SAAS,EAAC,iBAAiB;MAAAC,QAAA,eAC5BH,OAAA;QAAKE,SAAS,EAAC;MAAgB;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACrC,CAAC,eACNP,OAAA;MAAKE,SAAS,EAAC,WAAW;MAAAC,QAAA,eACtBH,OAAA;QAAKE,SAAS,EAAC,cAAc;QAAAC,QAAA,gBACzBH,OAAA;UAAKE,SAAS,EAAC,uBAAuB;UAAAC,QAAA,eAClCH,OAAA;YAAAG,QAAA,EAAM;UAAkC;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9C,CAAC,eACNP,OAAA;UAAIE,SAAS,EAAC,uBAAuB;UAAAC,QAAA,GAAC,cACtB,eAAAH,OAAA;YAAME,SAAS,EAAC,eAAe;YAAAC,QAAA,EAAC;UAAK;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACxDP,OAAA;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACNP,OAAA;YAAME,SAAS,EAAC,eAAe;YAAAC,QAAA,EAAC;UAAY;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnD,CAAC,eACLP,OAAA;UAAGE,SAAS,EAAC,0BAA0B;UAAAC,QAAA,EAAC;QAGxC;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eACJP,OAAA;UAAKE,SAAS,EAAC,yBAAyB;UAAAC,QAAA,gBACpCH,OAAA;YAAGQ,IAAI,EAAC,SAAS;YAACN,SAAS,EAAC,0BAA0B;YAAAC,QAAA,GAAC,kBAEnD,eAAAH,OAAA;cAAME,SAAS,EAAC,UAAU;cAAAC,QAAA,EAAC;YAAC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpC,CAAC,eACJP,OAAA;YAAGQ,IAAI,EAAC,WAAW;YAACN,SAAS,EAAC,WAAW;YAAAC,QAAA,gBACrCH,OAAA;cAAME,SAAS,EAAC,UAAU;cAAAC,QAAA,EAAC;YAAC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,cAEvC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACNP,OAAA;UAAKE,SAAS,EAAC,uBAAuB;UAAAC,QAAA,gBAClCH,OAAA;YAAKE,SAAS,EAAC,WAAW;YAAAC,QAAA,gBACtBH,OAAA;cAAME,SAAS,EAAC,aAAa;cAAAC,QAAA,EAAC;YAAI;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACzCP,OAAA;cAAME,SAAS,EAAC,YAAY;cAAAC,QAAA,EAAC;YAAY;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/C,CAAC,eACNP,OAAA;YAAKE,SAAS,EAAC,WAAW;YAAAC,QAAA,gBACtBH,OAAA;cAAME,SAAS,EAAC,aAAa;cAAAC,QAAA,EAAC;YAAK;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAC1CP,OAAA;cAAME,SAAS,EAAC,YAAY;cAAAC,QAAA,EAAC;YAAa;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChD,CAAC,eACNP,OAAA;YAAKE,SAAS,EAAC,WAAW;YAAAC,QAAA,gBACtBH,OAAA;cAAME,SAAS,EAAC,aAAa;cAAAC,QAAA,EAAC;YAAI;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACzCP,OAAA;cAAME,SAAS,EAAC,YAAY;cAAAC,QAAA,EAAC;YAAW;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9C,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACD,CAAC;AAElB;AAACE,EAAA,GAhDQR,IAAI;AAkDb,eAAeA,IAAI;AAAC,IAAAQ,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}