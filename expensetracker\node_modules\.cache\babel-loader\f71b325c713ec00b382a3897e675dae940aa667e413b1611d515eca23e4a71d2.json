{"ast": null, "code": "var _jsxFileName = \"D:\\\\Expense Tracker App (MERN )\\\\expensetracker\\\\src\\\\pages\\\\EmployeeDashboard.js\",\n  _s = $RefreshSig$();\nimport { useState } from 'react';\nimport './Dashboard.css';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nfunction EmployeeDashboard() {\n  _s();\n  const [activeTab, setActiveTab] = useState('submit');\n  const [expenses, setExpenses] = useState([{\n    id: 1,\n    amount: 850,\n    category: 'Travel',\n    description: 'Taxi fare to client meeting',\n    date: '2024-01-15',\n    status: 'Approved',\n    receipt: null\n  }, {\n    id: 2,\n    amount: 1200,\n    category: 'Meals',\n    description: 'Team lunch with clients',\n    date: '2024-01-14',\n    status: 'Pending',\n    receipt: 'receipt_001.pdf'\n  }, {\n    id: 3,\n    amount: 2500,\n    category: 'Equipment',\n    description: 'Laptop accessories',\n    date: '2024-01-12',\n    status: 'Reimbursed',\n    receipt: 'receipt_002.pdf'\n  }]);\n  const [newExpense, setNewExpense] = useState({\n    amount: '',\n    category: '',\n    description: '',\n    receipt: null\n  });\n  const handleSubmitExpense = e => {\n    e.preventDefault();\n    const expense = {\n      id: expenses.length + 1,\n      ...newExpense,\n      date: new Date().toISOString().split('T')[0],\n      status: 'Pending'\n    };\n    setExpenses([expense, ...expenses]);\n    setNewExpense({\n      amount: '',\n      category: '',\n      description: '',\n      receipt: null\n    });\n    alert('Expense submitted successfully!');\n  };\n  const getStatusColor = status => {\n    switch (status) {\n      case 'Approved':\n        return 'var(--accent-tertiary)';\n      case 'Rejected':\n        return 'var(--accent-secondary)';\n      case 'Reimbursed':\n        return 'var(--accent-primary)';\n      default:\n        return 'var(--text-secondary)';\n    }\n  };\n  const tabs = [{\n    id: 'submit',\n    label: 'Submit Expense',\n    icon: '➕'\n  }, {\n    id: 'expenses',\n    label: 'My Expenses',\n    icon: '📋'\n  }, {\n    id: 'reimbursements',\n    label: 'Reimbursements',\n    icon: '💰'\n  }];\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"dashboard-container\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"dashboard-background\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"dashboard-particles\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 74,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 73,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"dashboard-content\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"dashboard-header\",\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          className: \"dashboard-title\",\n          children: [\"Employee \", /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"gradient-text\",\n            children: \"Dashboard\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 80,\n            columnNumber: 34\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 79,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"dashboard-subtitle\",\n          children: \"Manage your expenses and track reimbursements\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 82,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 78,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"dashboard-tabs\",\n        children: tabs.map(tab => /*#__PURE__*/_jsxDEV(\"button\", {\n          className: `tab-btn ${activeTab === tab.id ? 'active' : ''}`,\n          onClick: () => setActiveTab(tab.id),\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"tab-icon\",\n            children: tab.icon\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 92,\n            columnNumber: 29\n          }, this), tab.label]\n        }, tab.id, true, {\n          fileName: _jsxFileName,\n          lineNumber: 87,\n          columnNumber: 25\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 85,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"dashboard-main\",\n        children: [activeTab === 'submit' && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"tab-content\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"content-card\",\n            children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n              className: \"card-title\",\n              children: \"Submit New Expense\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 102,\n              columnNumber: 33\n            }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n              onSubmit: handleSubmitExpense,\n              className: \"expense-form\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-row\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"form-group\",\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    className: \"form-label\",\n                    children: \"Amount (\\u20B9)\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 106,\n                    columnNumber: 45\n                  }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"number\",\n                    className: \"form-input\",\n                    value: newExpense.amount,\n                    onChange: e => setNewExpense({\n                      ...newExpense,\n                      amount: e.target.value\n                    }),\n                    placeholder: \"0.00\",\n                    required: true\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 107,\n                    columnNumber: 45\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 105,\n                  columnNumber: 41\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"form-group\",\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    className: \"form-label\",\n                    children: \"Category\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 117,\n                    columnNumber: 45\n                  }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                    className: \"form-input\",\n                    value: newExpense.category,\n                    onChange: e => setNewExpense({\n                      ...newExpense,\n                      category: e.target.value\n                    }),\n                    required: true,\n                    children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                      value: \"\",\n                      children: \"Select Category\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 124,\n                      columnNumber: 49\n                    }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                      value: \"Travel\",\n                      children: \"Travel\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 125,\n                      columnNumber: 49\n                    }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                      value: \"Meals\",\n                      children: \"Meals\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 126,\n                      columnNumber: 49\n                    }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                      value: \"Equipment\",\n                      children: \"Equipment\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 127,\n                      columnNumber: 49\n                    }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                      value: \"Office Supplies\",\n                      children: \"Office Supplies\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 128,\n                      columnNumber: 49\n                    }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                      value: \"Training\",\n                      children: \"Training\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 129,\n                      columnNumber: 49\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 118,\n                    columnNumber: 45\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 116,\n                  columnNumber: 41\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 104,\n                columnNumber: 37\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"form-label\",\n                  children: \"Description\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 134,\n                  columnNumber: 41\n                }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n                  className: \"form-input\",\n                  value: newExpense.description,\n                  onChange: e => setNewExpense({\n                    ...newExpense,\n                    description: e.target.value\n                  }),\n                  placeholder: \"Describe your expense...\",\n                  rows: \"3\",\n                  required: true\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 135,\n                  columnNumber: 41\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 133,\n                columnNumber: 37\n              }, this), newExpense.amount > 1000 && /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"form-label\",\n                  children: \"Receipt (Required for amounts > \\u20B91000)\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 146,\n                  columnNumber: 45\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"file\",\n                  className: \"form-input file-input\",\n                  accept: \".pdf,.jpg,.jpeg,.png\",\n                  onChange: e => setNewExpense({\n                    ...newExpense,\n                    receipt: e.target.files[0]\n                  }),\n                  required: true\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 147,\n                  columnNumber: 45\n                }, this), /*#__PURE__*/_jsxDEV(\"small\", {\n                  className: \"form-help\",\n                  children: \"Upload PDF or image file\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 154,\n                  columnNumber: 45\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 145,\n                columnNumber: 41\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                type: \"submit\",\n                className: \"submit-btn\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"btn-icon\",\n                  children: \"\\uD83D\\uDCE4\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 158,\n                  columnNumber: 41\n                }, this), \"Submit Expense\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 157,\n                columnNumber: 37\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 103,\n              columnNumber: 33\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 101,\n            columnNumber: 29\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 100,\n          columnNumber: 25\n        }, this), activeTab === 'expenses' && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"tab-content\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"content-card\",\n            children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n              className: \"card-title\",\n              children: \"My Expenses\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 169,\n              columnNumber: 33\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"expenses-list\",\n              children: expenses.map(expense => /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"expense-item\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"expense-info\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"expense-header\",\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"expense-amount\",\n                      children: [\"\\u20B9\", expense.amount]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 175,\n                      columnNumber: 53\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"expense-status\",\n                      style: {\n                        color: getStatusColor(expense.status)\n                      },\n                      children: expense.status\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 176,\n                      columnNumber: 53\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 174,\n                    columnNumber: 49\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"expense-details\",\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"expense-category\",\n                      children: expense.category\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 184,\n                      columnNumber: 53\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"expense-date\",\n                      children: expense.date\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 185,\n                      columnNumber: 53\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 183,\n                    columnNumber: 49\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"expense-description\",\n                    children: expense.description\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 187,\n                    columnNumber: 49\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 173,\n                  columnNumber: 45\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"expense-actions\",\n                  children: [expense.status === 'Pending' && /*#__PURE__*/_jsxDEV(_Fragment, {\n                    children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                      className: \"action-btn edit-btn\",\n                      children: \"\\u270F\\uFE0F Edit\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 192,\n                      columnNumber: 57\n                    }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                      className: \"action-btn delete-btn\",\n                      children: \"\\uD83D\\uDDD1\\uFE0F Delete\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 193,\n                      columnNumber: 57\n                    }, this)]\n                  }, void 0, true), expense.receipt && /*#__PURE__*/_jsxDEV(\"button\", {\n                    className: \"action-btn view-btn\",\n                    children: \"\\uD83D\\uDCC4 View Receipt\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 197,\n                    columnNumber: 53\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 189,\n                  columnNumber: 45\n                }, this)]\n              }, expense.id, true, {\n                fileName: _jsxFileName,\n                lineNumber: 172,\n                columnNumber: 41\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 170,\n              columnNumber: 33\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 168,\n            columnNumber: 29\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 167,\n          columnNumber: 25\n        }, this), activeTab === 'reimbursements' && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"tab-content\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"content-card\",\n            children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n              className: \"card-title\",\n              children: \"Reimbursement Status\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 210,\n              columnNumber: 33\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"reimbursement-summary\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"summary-card\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"summary-icon\",\n                  children: \"\\uD83D\\uDCB0\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 213,\n                  columnNumber: 41\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"summary-info\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"summary-label\",\n                    children: \"Total Reimbursed\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 215,\n                    columnNumber: 45\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"summary-value\",\n                    children: \"\\u20B92,500\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 216,\n                    columnNumber: 45\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 214,\n                  columnNumber: 41\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 212,\n                columnNumber: 37\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"summary-card\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"summary-icon\",\n                  children: \"\\u23F3\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 220,\n                  columnNumber: 41\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"summary-info\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"summary-label\",\n                    children: \"Pending Amount\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 222,\n                    columnNumber: 45\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"summary-value\",\n                    children: \"\\u20B92,050\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 223,\n                    columnNumber: 45\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 221,\n                  columnNumber: 41\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 219,\n                columnNumber: 37\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"summary-card\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"summary-icon\",\n                  children: \"\\u2705\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 227,\n                  columnNumber: 41\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"summary-info\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"summary-label\",\n                    children: \"Approved Amount\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 229,\n                    columnNumber: 45\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"summary-value\",\n                    children: \"\\u20B9850\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 230,\n                    columnNumber: 45\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 228,\n                  columnNumber: 41\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 226,\n                columnNumber: 37\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 211,\n              columnNumber: 33\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"reimbursement-list\",\n              children: expenses.filter(e => e.status === 'Reimbursed').map(expense => /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"reimbursement-item\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"reimbursement-info\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"reimbursement-amount\",\n                    children: [\"\\u20B9\", expense.amount]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 238,\n                    columnNumber: 49\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"reimbursement-category\",\n                    children: expense.category\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 239,\n                    columnNumber: 49\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"reimbursement-date\",\n                    children: expense.date\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 240,\n                    columnNumber: 49\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 237,\n                  columnNumber: 45\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  className: \"action-btn download-btn\",\n                  children: \"\\uD83D\\uDCE5 Download Proof\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 242,\n                  columnNumber: 45\n                }, this)]\n              }, expense.id, true, {\n                fileName: _jsxFileName,\n                lineNumber: 236,\n                columnNumber: 41\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 234,\n              columnNumber: 33\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 209,\n            columnNumber: 29\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 208,\n          columnNumber: 25\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 98,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 77,\n      columnNumber: 13\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 72,\n    columnNumber: 9\n  }, this);\n}\n_s(EmployeeDashboard, \"4Tfez5f/xZefxTXCMQpArKVwrRc=\");\n_c = EmployeeDashboard;\nexport default EmployeeDashboard;\nvar _c;\n$RefreshReg$(_c, \"EmployeeDashboard\");", "map": {"version": 3, "names": ["useState", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "EmployeeDashboard", "_s", "activeTab", "setActiveTab", "expenses", "setExpenses", "id", "amount", "category", "description", "date", "status", "receipt", "newExpense", "setNewExpense", "handleSubmitExpense", "e", "preventDefault", "expense", "length", "Date", "toISOString", "split", "alert", "getStatusColor", "tabs", "label", "icon", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "map", "tab", "onClick", "onSubmit", "type", "value", "onChange", "target", "placeholder", "required", "rows", "accept", "files", "style", "color", "filter", "_c", "$RefreshReg$"], "sources": ["D:/Expense Tracker App (MERN )/expensetracker/src/pages/EmployeeDashboard.js"], "sourcesContent": ["import { useState } from 'react';\nimport './Dashboard.css';\n\nfunction EmployeeDashboard() {\n    const [activeTab, setActiveTab] = useState('submit');\n    const [expenses, setExpenses] = useState([\n        {\n            id: 1,\n            amount: 850,\n            category: 'Travel',\n            description: 'Taxi fare to client meeting',\n            date: '2024-01-15',\n            status: 'Approved',\n            receipt: null\n        },\n        {\n            id: 2,\n            amount: 1200,\n            category: 'Meals',\n            description: 'Team lunch with clients',\n            date: '2024-01-14',\n            status: 'Pending',\n            receipt: 'receipt_001.pdf'\n        },\n        {\n            id: 3,\n            amount: 2500,\n            category: 'Equipment',\n            description: 'Laptop accessories',\n            date: '2024-01-12',\n            status: 'Reimbursed',\n            receipt: 'receipt_002.pdf'\n        }\n    ]);\n\n    const [newExpense, setNewExpense] = useState({\n        amount: '',\n        category: '',\n        description: '',\n        receipt: null\n    });\n\n    const handleSubmitExpense = (e) => {\n        e.preventDefault();\n        const expense = {\n            id: expenses.length + 1,\n            ...newExpense,\n            date: new Date().toISOString().split('T')[0],\n            status: 'Pending'\n        };\n        setExpenses([expense, ...expenses]);\n        setNewExpense({ amount: '', category: '', description: '', receipt: null });\n        alert('Expense submitted successfully!');\n    };\n\n    const getStatusColor = (status) => {\n        switch (status) {\n            case 'Approved': return 'var(--accent-tertiary)';\n            case 'Rejected': return 'var(--accent-secondary)';\n            case 'Reimbursed': return 'var(--accent-primary)';\n            default: return 'var(--text-secondary)';\n        }\n    };\n\n    const tabs = [\n        { id: 'submit', label: 'Submit Expense', icon: '➕' },\n        { id: 'expenses', label: 'My Expenses', icon: '📋' },\n        { id: 'reimbursements', label: 'Reimbursements', icon: '💰' }\n    ];\n\n    return (\n        <div className=\"dashboard-container\">\n            <div className=\"dashboard-background\">\n                <div className=\"dashboard-particles\"></div>\n            </div>\n\n            <div className=\"dashboard-content\">\n                <div className=\"dashboard-header\">\n                    <h1 className=\"dashboard-title\">\n                        Employee <span className=\"gradient-text\">Dashboard</span>\n                    </h1>\n                    <p className=\"dashboard-subtitle\">Manage your expenses and track reimbursements</p>\n                </div>\n\n                <div className=\"dashboard-tabs\">\n                    {tabs.map(tab => (\n                        <button\n                            key={tab.id}\n                            className={`tab-btn ${activeTab === tab.id ? 'active' : ''}`}\n                            onClick={() => setActiveTab(tab.id)}\n                        >\n                            <span className=\"tab-icon\">{tab.icon}</span>\n                            {tab.label}\n                        </button>\n                    ))}\n                </div>\n\n                <div className=\"dashboard-main\">\n                    {activeTab === 'submit' && (\n                        <div className=\"tab-content\">\n                            <div className=\"content-card\">\n                                <h2 className=\"card-title\">Submit New Expense</h2>\n                                <form onSubmit={handleSubmitExpense} className=\"expense-form\">\n                                    <div className=\"form-row\">\n                                        <div className=\"form-group\">\n                                            <label className=\"form-label\">Amount (₹)</label>\n                                            <input\n                                                type=\"number\"\n                                                className=\"form-input\"\n                                                value={newExpense.amount}\n                                                onChange={(e) => setNewExpense({...newExpense, amount: e.target.value})}\n                                                placeholder=\"0.00\"\n                                                required\n                                            />\n                                        </div>\n                                        <div className=\"form-group\">\n                                            <label className=\"form-label\">Category</label>\n                                            <select\n                                                className=\"form-input\"\n                                                value={newExpense.category}\n                                                onChange={(e) => setNewExpense({...newExpense, category: e.target.value})}\n                                                required\n                                            >\n                                                <option value=\"\">Select Category</option>\n                                                <option value=\"Travel\">Travel</option>\n                                                <option value=\"Meals\">Meals</option>\n                                                <option value=\"Equipment\">Equipment</option>\n                                                <option value=\"Office Supplies\">Office Supplies</option>\n                                                <option value=\"Training\">Training</option>\n                                            </select>\n                                        </div>\n                                    </div>\n                                    <div className=\"form-group\">\n                                        <label className=\"form-label\">Description</label>\n                                        <textarea\n                                            className=\"form-input\"\n                                            value={newExpense.description}\n                                            onChange={(e) => setNewExpense({...newExpense, description: e.target.value})}\n                                            placeholder=\"Describe your expense...\"\n                                            rows=\"3\"\n                                            required\n                                        />\n                                    </div>\n                                    {newExpense.amount > 1000 && (\n                                        <div className=\"form-group\">\n                                            <label className=\"form-label\">Receipt (Required for amounts > ₹1000)</label>\n                                            <input\n                                                type=\"file\"\n                                                className=\"form-input file-input\"\n                                                accept=\".pdf,.jpg,.jpeg,.png\"\n                                                onChange={(e) => setNewExpense({...newExpense, receipt: e.target.files[0]})}\n                                                required\n                                            />\n                                            <small className=\"form-help\">Upload PDF or image file</small>\n                                        </div>\n                                    )}\n                                    <button type=\"submit\" className=\"submit-btn\">\n                                        <span className=\"btn-icon\">📤</span>\n                                        Submit Expense\n                                    </button>\n                                </form>\n                            </div>\n                        </div>\n                    )}\n\n                    {activeTab === 'expenses' && (\n                        <div className=\"tab-content\">\n                            <div className=\"content-card\">\n                                <h2 className=\"card-title\">My Expenses</h2>\n                                <div className=\"expenses-list\">\n                                    {expenses.map(expense => (\n                                        <div key={expense.id} className=\"expense-item\">\n                                            <div className=\"expense-info\">\n                                                <div className=\"expense-header\">\n                                                    <span className=\"expense-amount\">₹{expense.amount}</span>\n                                                    <span \n                                                        className=\"expense-status\"\n                                                        style={{ color: getStatusColor(expense.status) }}\n                                                    >\n                                                        {expense.status}\n                                                    </span>\n                                                </div>\n                                                <div className=\"expense-details\">\n                                                    <span className=\"expense-category\">{expense.category}</span>\n                                                    <span className=\"expense-date\">{expense.date}</span>\n                                                </div>\n                                                <p className=\"expense-description\">{expense.description}</p>\n                                            </div>\n                                            <div className=\"expense-actions\">\n                                                {expense.status === 'Pending' && (\n                                                    <>\n                                                        <button className=\"action-btn edit-btn\">✏️ Edit</button>\n                                                        <button className=\"action-btn delete-btn\">🗑️ Delete</button>\n                                                    </>\n                                                )}\n                                                {expense.receipt && (\n                                                    <button className=\"action-btn view-btn\">📄 View Receipt</button>\n                                                )}\n                                            </div>\n                                        </div>\n                                    ))}\n                                </div>\n                            </div>\n                        </div>\n                    )}\n\n                    {activeTab === 'reimbursements' && (\n                        <div className=\"tab-content\">\n                            <div className=\"content-card\">\n                                <h2 className=\"card-title\">Reimbursement Status</h2>\n                                <div className=\"reimbursement-summary\">\n                                    <div className=\"summary-card\">\n                                        <div className=\"summary-icon\">💰</div>\n                                        <div className=\"summary-info\">\n                                            <span className=\"summary-label\">Total Reimbursed</span>\n                                            <span className=\"summary-value\">₹2,500</span>\n                                        </div>\n                                    </div>\n                                    <div className=\"summary-card\">\n                                        <div className=\"summary-icon\">⏳</div>\n                                        <div className=\"summary-info\">\n                                            <span className=\"summary-label\">Pending Amount</span>\n                                            <span className=\"summary-value\">₹2,050</span>\n                                        </div>\n                                    </div>\n                                    <div className=\"summary-card\">\n                                        <div className=\"summary-icon\">✅</div>\n                                        <div className=\"summary-info\">\n                                            <span className=\"summary-label\">Approved Amount</span>\n                                            <span className=\"summary-value\">₹850</span>\n                                        </div>\n                                    </div>\n                                </div>\n                                <div className=\"reimbursement-list\">\n                                    {expenses.filter(e => e.status === 'Reimbursed').map(expense => (\n                                        <div key={expense.id} className=\"reimbursement-item\">\n                                            <div className=\"reimbursement-info\">\n                                                <span className=\"reimbursement-amount\">₹{expense.amount}</span>\n                                                <span className=\"reimbursement-category\">{expense.category}</span>\n                                                <span className=\"reimbursement-date\">{expense.date}</span>\n                                            </div>\n                                            <button className=\"action-btn download-btn\">📥 Download Proof</button>\n                                        </div>\n                                    ))}\n                                </div>\n                            </div>\n                        </div>\n                    )}\n                </div>\n            </div>\n        </div>\n    );\n}\n\nexport default EmployeeDashboard;\n"], "mappings": ";;AAAA,SAASA,QAAQ,QAAQ,OAAO;AAChC,OAAO,iBAAiB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAEzB,SAASC,iBAAiBA,CAAA,EAAG;EAAAC,EAAA;EACzB,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAGR,QAAQ,CAAC,QAAQ,CAAC;EACpD,MAAM,CAACS,QAAQ,EAAEC,WAAW,CAAC,GAAGV,QAAQ,CAAC,CACrC;IACIW,EAAE,EAAE,CAAC;IACLC,MAAM,EAAE,GAAG;IACXC,QAAQ,EAAE,QAAQ;IAClBC,WAAW,EAAE,6BAA6B;IAC1CC,IAAI,EAAE,YAAY;IAClBC,MAAM,EAAE,UAAU;IAClBC,OAAO,EAAE;EACb,CAAC,EACD;IACIN,EAAE,EAAE,CAAC;IACLC,MAAM,EAAE,IAAI;IACZC,QAAQ,EAAE,OAAO;IACjBC,WAAW,EAAE,yBAAyB;IACtCC,IAAI,EAAE,YAAY;IAClBC,MAAM,EAAE,SAAS;IACjBC,OAAO,EAAE;EACb,CAAC,EACD;IACIN,EAAE,EAAE,CAAC;IACLC,MAAM,EAAE,IAAI;IACZC,QAAQ,EAAE,WAAW;IACrBC,WAAW,EAAE,oBAAoB;IACjCC,IAAI,EAAE,YAAY;IAClBC,MAAM,EAAE,YAAY;IACpBC,OAAO,EAAE;EACb,CAAC,CACJ,CAAC;EAEF,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAGnB,QAAQ,CAAC;IACzCY,MAAM,EAAE,EAAE;IACVC,QAAQ,EAAE,EAAE;IACZC,WAAW,EAAE,EAAE;IACfG,OAAO,EAAE;EACb,CAAC,CAAC;EAEF,MAAMG,mBAAmB,GAAIC,CAAC,IAAK;IAC/BA,CAAC,CAACC,cAAc,CAAC,CAAC;IAClB,MAAMC,OAAO,GAAG;MACZZ,EAAE,EAAEF,QAAQ,CAACe,MAAM,GAAG,CAAC;MACvB,GAAGN,UAAU;MACbH,IAAI,EAAE,IAAIU,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;MAC5CX,MAAM,EAAE;IACZ,CAAC;IACDN,WAAW,CAAC,CAACa,OAAO,EAAE,GAAGd,QAAQ,CAAC,CAAC;IACnCU,aAAa,CAAC;MAAEP,MAAM,EAAE,EAAE;MAAEC,QAAQ,EAAE,EAAE;MAAEC,WAAW,EAAE,EAAE;MAAEG,OAAO,EAAE;IAAK,CAAC,CAAC;IAC3EW,KAAK,CAAC,iCAAiC,CAAC;EAC5C,CAAC;EAED,MAAMC,cAAc,GAAIb,MAAM,IAAK;IAC/B,QAAQA,MAAM;MACV,KAAK,UAAU;QAAE,OAAO,wBAAwB;MAChD,KAAK,UAAU;QAAE,OAAO,yBAAyB;MACjD,KAAK,YAAY;QAAE,OAAO,uBAAuB;MACjD;QAAS,OAAO,uBAAuB;IAC3C;EACJ,CAAC;EAED,MAAMc,IAAI,GAAG,CACT;IAAEnB,EAAE,EAAE,QAAQ;IAAEoB,KAAK,EAAE,gBAAgB;IAAEC,IAAI,EAAE;EAAI,CAAC,EACpD;IAAErB,EAAE,EAAE,UAAU;IAAEoB,KAAK,EAAE,aAAa;IAAEC,IAAI,EAAE;EAAK,CAAC,EACpD;IAAErB,EAAE,EAAE,gBAAgB;IAAEoB,KAAK,EAAE,gBAAgB;IAAEC,IAAI,EAAE;EAAK,CAAC,CAChE;EAED,oBACI9B,OAAA;IAAK+B,SAAS,EAAC,qBAAqB;IAAAC,QAAA,gBAChChC,OAAA;MAAK+B,SAAS,EAAC,sBAAsB;MAAAC,QAAA,eACjChC,OAAA;QAAK+B,SAAS,EAAC;MAAqB;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC1C,CAAC,eAENpC,OAAA;MAAK+B,SAAS,EAAC,mBAAmB;MAAAC,QAAA,gBAC9BhC,OAAA;QAAK+B,SAAS,EAAC,kBAAkB;QAAAC,QAAA,gBAC7BhC,OAAA;UAAI+B,SAAS,EAAC,iBAAiB;UAAAC,QAAA,GAAC,WACnB,eAAAhC,OAAA;YAAM+B,SAAS,EAAC,eAAe;YAAAC,QAAA,EAAC;UAAS;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzD,CAAC,eACLpC,OAAA;UAAG+B,SAAS,EAAC,oBAAoB;UAAAC,QAAA,EAAC;QAA6C;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAClF,CAAC,eAENpC,OAAA;QAAK+B,SAAS,EAAC,gBAAgB;QAAAC,QAAA,EAC1BJ,IAAI,CAACS,GAAG,CAACC,GAAG,iBACTtC,OAAA;UAEI+B,SAAS,EAAE,WAAW1B,SAAS,KAAKiC,GAAG,CAAC7B,EAAE,GAAG,QAAQ,GAAG,EAAE,EAAG;UAC7D8B,OAAO,EAAEA,CAAA,KAAMjC,YAAY,CAACgC,GAAG,CAAC7B,EAAE,CAAE;UAAAuB,QAAA,gBAEpChC,OAAA;YAAM+B,SAAS,EAAC,UAAU;YAAAC,QAAA,EAAEM,GAAG,CAACR;UAAI;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,EAC3CE,GAAG,CAACT,KAAK;QAAA,GALLS,GAAG,CAAC7B,EAAE;UAAAwB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAMP,CACX;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,eAENpC,OAAA;QAAK+B,SAAS,EAAC,gBAAgB;QAAAC,QAAA,GAC1B3B,SAAS,KAAK,QAAQ,iBACnBL,OAAA;UAAK+B,SAAS,EAAC,aAAa;UAAAC,QAAA,eACxBhC,OAAA;YAAK+B,SAAS,EAAC,cAAc;YAAAC,QAAA,gBACzBhC,OAAA;cAAI+B,SAAS,EAAC,YAAY;cAAAC,QAAA,EAAC;YAAkB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAClDpC,OAAA;cAAMwC,QAAQ,EAAEtB,mBAAoB;cAACa,SAAS,EAAC,cAAc;cAAAC,QAAA,gBACzDhC,OAAA;gBAAK+B,SAAS,EAAC,UAAU;gBAAAC,QAAA,gBACrBhC,OAAA;kBAAK+B,SAAS,EAAC,YAAY;kBAAAC,QAAA,gBACvBhC,OAAA;oBAAO+B,SAAS,EAAC,YAAY;oBAAAC,QAAA,EAAC;kBAAU;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eAChDpC,OAAA;oBACIyC,IAAI,EAAC,QAAQ;oBACbV,SAAS,EAAC,YAAY;oBACtBW,KAAK,EAAE1B,UAAU,CAACN,MAAO;oBACzBiC,QAAQ,EAAGxB,CAAC,IAAKF,aAAa,CAAC;sBAAC,GAAGD,UAAU;sBAAEN,MAAM,EAAES,CAAC,CAACyB,MAAM,CAACF;oBAAK,CAAC,CAAE;oBACxEG,WAAW,EAAC,MAAM;oBAClBC,QAAQ;kBAAA;oBAAAb,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACX,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACD,CAAC,eACNpC,OAAA;kBAAK+B,SAAS,EAAC,YAAY;kBAAAC,QAAA,gBACvBhC,OAAA;oBAAO+B,SAAS,EAAC,YAAY;oBAAAC,QAAA,EAAC;kBAAQ;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eAC9CpC,OAAA;oBACI+B,SAAS,EAAC,YAAY;oBACtBW,KAAK,EAAE1B,UAAU,CAACL,QAAS;oBAC3BgC,QAAQ,EAAGxB,CAAC,IAAKF,aAAa,CAAC;sBAAC,GAAGD,UAAU;sBAAEL,QAAQ,EAAEQ,CAAC,CAACyB,MAAM,CAACF;oBAAK,CAAC,CAAE;oBAC1EI,QAAQ;oBAAAd,QAAA,gBAERhC,OAAA;sBAAQ0C,KAAK,EAAC,EAAE;sBAAAV,QAAA,EAAC;oBAAe;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,eACzCpC,OAAA;sBAAQ0C,KAAK,EAAC,QAAQ;sBAAAV,QAAA,EAAC;oBAAM;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,eACtCpC,OAAA;sBAAQ0C,KAAK,EAAC,OAAO;sBAAAV,QAAA,EAAC;oBAAK;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,eACpCpC,OAAA;sBAAQ0C,KAAK,EAAC,WAAW;sBAAAV,QAAA,EAAC;oBAAS;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,eAC5CpC,OAAA;sBAAQ0C,KAAK,EAAC,iBAAiB;sBAAAV,QAAA,EAAC;oBAAe;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,eACxDpC,OAAA;sBAAQ0C,KAAK,EAAC,UAAU;sBAAAV,QAAA,EAAC;oBAAQ;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACtC,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACR,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC,eACNpC,OAAA;gBAAK+B,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACvBhC,OAAA;kBAAO+B,SAAS,EAAC,YAAY;kBAAAC,QAAA,EAAC;gBAAW;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACjDpC,OAAA;kBACI+B,SAAS,EAAC,YAAY;kBACtBW,KAAK,EAAE1B,UAAU,CAACJ,WAAY;kBAC9B+B,QAAQ,EAAGxB,CAAC,IAAKF,aAAa,CAAC;oBAAC,GAAGD,UAAU;oBAAEJ,WAAW,EAAEO,CAAC,CAACyB,MAAM,CAACF;kBAAK,CAAC,CAAE;kBAC7EG,WAAW,EAAC,0BAA0B;kBACtCE,IAAI,EAAC,GAAG;kBACRD,QAAQ;gBAAA;kBAAAb,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACX,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD,CAAC,EACLpB,UAAU,CAACN,MAAM,GAAG,IAAI,iBACrBV,OAAA;gBAAK+B,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACvBhC,OAAA;kBAAO+B,SAAS,EAAC,YAAY;kBAAAC,QAAA,EAAC;gBAAsC;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAC5EpC,OAAA;kBACIyC,IAAI,EAAC,MAAM;kBACXV,SAAS,EAAC,uBAAuB;kBACjCiB,MAAM,EAAC,sBAAsB;kBAC7BL,QAAQ,EAAGxB,CAAC,IAAKF,aAAa,CAAC;oBAAC,GAAGD,UAAU;oBAAED,OAAO,EAAEI,CAAC,CAACyB,MAAM,CAACK,KAAK,CAAC,CAAC;kBAAC,CAAC,CAAE;kBAC5EH,QAAQ;gBAAA;kBAAAb,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACX,CAAC,eACFpC,OAAA;kBAAO+B,SAAS,EAAC,WAAW;kBAAAC,QAAA,EAAC;gBAAwB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5D,CACR,eACDpC,OAAA;gBAAQyC,IAAI,EAAC,QAAQ;gBAACV,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACxChC,OAAA;kBAAM+B,SAAS,EAAC,UAAU;kBAAAC,QAAA,EAAC;gBAAE;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,kBAExC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACP,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CACR,EAEA/B,SAAS,KAAK,UAAU,iBACrBL,OAAA;UAAK+B,SAAS,EAAC,aAAa;UAAAC,QAAA,eACxBhC,OAAA;YAAK+B,SAAS,EAAC,cAAc;YAAAC,QAAA,gBACzBhC,OAAA;cAAI+B,SAAS,EAAC,YAAY;cAAAC,QAAA,EAAC;YAAW;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC3CpC,OAAA;cAAK+B,SAAS,EAAC,eAAe;cAAAC,QAAA,EACzBzB,QAAQ,CAAC8B,GAAG,CAAChB,OAAO,iBACjBrB,OAAA;gBAAsB+B,SAAS,EAAC,cAAc;gBAAAC,QAAA,gBAC1ChC,OAAA;kBAAK+B,SAAS,EAAC,cAAc;kBAAAC,QAAA,gBACzBhC,OAAA;oBAAK+B,SAAS,EAAC,gBAAgB;oBAAAC,QAAA,gBAC3BhC,OAAA;sBAAM+B,SAAS,EAAC,gBAAgB;sBAAAC,QAAA,GAAC,QAAC,EAACX,OAAO,CAACX,MAAM;oBAAA;sBAAAuB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC,eACzDpC,OAAA;sBACI+B,SAAS,EAAC,gBAAgB;sBAC1BmB,KAAK,EAAE;wBAAEC,KAAK,EAAExB,cAAc,CAACN,OAAO,CAACP,MAAM;sBAAE,CAAE;sBAAAkB,QAAA,EAEhDX,OAAO,CAACP;oBAAM;sBAAAmB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACb,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACN,CAAC,eACNpC,OAAA;oBAAK+B,SAAS,EAAC,iBAAiB;oBAAAC,QAAA,gBAC5BhC,OAAA;sBAAM+B,SAAS,EAAC,kBAAkB;sBAAAC,QAAA,EAAEX,OAAO,CAACV;oBAAQ;sBAAAsB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC,eAC5DpC,OAAA;sBAAM+B,SAAS,EAAC,cAAc;sBAAAC,QAAA,EAAEX,OAAO,CAACR;oBAAI;sBAAAoB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACnD,CAAC,eACNpC,OAAA;oBAAG+B,SAAS,EAAC,qBAAqB;oBAAAC,QAAA,EAAEX,OAAO,CAACT;kBAAW;oBAAAqB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC3D,CAAC,eACNpC,OAAA;kBAAK+B,SAAS,EAAC,iBAAiB;kBAAAC,QAAA,GAC3BX,OAAO,CAACP,MAAM,KAAK,SAAS,iBACzBd,OAAA,CAAAE,SAAA;oBAAA8B,QAAA,gBACIhC,OAAA;sBAAQ+B,SAAS,EAAC,qBAAqB;sBAAAC,QAAA,EAAC;oBAAO;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,eACxDpC,OAAA;sBAAQ+B,SAAS,EAAC,uBAAuB;sBAAAC,QAAA,EAAC;oBAAU;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC;kBAAA,eAC/D,CACL,EACAf,OAAO,CAACN,OAAO,iBACZf,OAAA;oBAAQ+B,SAAS,EAAC,qBAAqB;oBAAAC,QAAA,EAAC;kBAAe;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAClE;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACA,CAAC;cAAA,GA3BAf,OAAO,CAACZ,EAAE;gBAAAwB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OA4Bf,CACR;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CACR,EAEA/B,SAAS,KAAK,gBAAgB,iBAC3BL,OAAA;UAAK+B,SAAS,EAAC,aAAa;UAAAC,QAAA,eACxBhC,OAAA;YAAK+B,SAAS,EAAC,cAAc;YAAAC,QAAA,gBACzBhC,OAAA;cAAI+B,SAAS,EAAC,YAAY;cAAAC,QAAA,EAAC;YAAoB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACpDpC,OAAA;cAAK+B,SAAS,EAAC,uBAAuB;cAAAC,QAAA,gBAClChC,OAAA;gBAAK+B,SAAS,EAAC,cAAc;gBAAAC,QAAA,gBACzBhC,OAAA;kBAAK+B,SAAS,EAAC,cAAc;kBAAAC,QAAA,EAAC;gBAAE;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACtCpC,OAAA;kBAAK+B,SAAS,EAAC,cAAc;kBAAAC,QAAA,gBACzBhC,OAAA;oBAAM+B,SAAS,EAAC,eAAe;oBAAAC,QAAA,EAAC;kBAAgB;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eACvDpC,OAAA;oBAAM+B,SAAS,EAAC,eAAe;oBAAAC,QAAA,EAAC;kBAAM;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC5C,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC,eACNpC,OAAA;gBAAK+B,SAAS,EAAC,cAAc;gBAAAC,QAAA,gBACzBhC,OAAA;kBAAK+B,SAAS,EAAC,cAAc;kBAAAC,QAAA,EAAC;gBAAC;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACrCpC,OAAA;kBAAK+B,SAAS,EAAC,cAAc;kBAAAC,QAAA,gBACzBhC,OAAA;oBAAM+B,SAAS,EAAC,eAAe;oBAAAC,QAAA,EAAC;kBAAc;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eACrDpC,OAAA;oBAAM+B,SAAS,EAAC,eAAe;oBAAAC,QAAA,EAAC;kBAAM;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC5C,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC,eACNpC,OAAA;gBAAK+B,SAAS,EAAC,cAAc;gBAAAC,QAAA,gBACzBhC,OAAA;kBAAK+B,SAAS,EAAC,cAAc;kBAAAC,QAAA,EAAC;gBAAC;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACrCpC,OAAA;kBAAK+B,SAAS,EAAC,cAAc;kBAAAC,QAAA,gBACzBhC,OAAA;oBAAM+B,SAAS,EAAC,eAAe;oBAAAC,QAAA,EAAC;kBAAe;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eACtDpC,OAAA;oBAAM+B,SAAS,EAAC,eAAe;oBAAAC,QAAA,EAAC;kBAAI;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1C,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC,eACNpC,OAAA;cAAK+B,SAAS,EAAC,oBAAoB;cAAAC,QAAA,EAC9BzB,QAAQ,CAAC6C,MAAM,CAACjC,CAAC,IAAIA,CAAC,CAACL,MAAM,KAAK,YAAY,CAAC,CAACuB,GAAG,CAAChB,OAAO,iBACxDrB,OAAA;gBAAsB+B,SAAS,EAAC,oBAAoB;gBAAAC,QAAA,gBAChDhC,OAAA;kBAAK+B,SAAS,EAAC,oBAAoB;kBAAAC,QAAA,gBAC/BhC,OAAA;oBAAM+B,SAAS,EAAC,sBAAsB;oBAAAC,QAAA,GAAC,QAAC,EAACX,OAAO,CAACX,MAAM;kBAAA;oBAAAuB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eAC/DpC,OAAA;oBAAM+B,SAAS,EAAC,wBAAwB;oBAAAC,QAAA,EAAEX,OAAO,CAACV;kBAAQ;oBAAAsB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eAClEpC,OAAA;oBAAM+B,SAAS,EAAC,oBAAoB;oBAAAC,QAAA,EAAEX,OAAO,CAACR;kBAAI;oBAAAoB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACzD,CAAC,eACNpC,OAAA;kBAAQ+B,SAAS,EAAC,yBAAyB;kBAAAC,QAAA,EAAC;gBAAiB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA,GANhEf,OAAO,CAACZ,EAAE;gBAAAwB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAOf,CACR;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CACR;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;AAEd;AAAChC,EAAA,CAzPQD,iBAAiB;AAAAkD,EAAA,GAAjBlD,iBAAiB;AA2P1B,eAAeA,iBAAiB;AAAC,IAAAkD,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}