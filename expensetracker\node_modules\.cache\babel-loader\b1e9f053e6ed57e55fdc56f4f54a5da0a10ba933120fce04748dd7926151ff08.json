{"ast": null, "code": "var _jsxFileName = \"D:\\\\Expense Tracker App (MERN )\\\\expensetracker\\\\src\\\\components\\\\Navbar.js\",\n  _s = $RefreshSig$();\nimport { Link } from \"react-router-dom\";\nimport { useState } from \"react\";\nimport './Navbar.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction Navbar() {\n  _s();\n  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);\n  const toggleMobileMenu = () => {\n    setIsMobileMenuOpen(!isMobileMenuOpen);\n  };\n  const closeMobileMenu = () => {\n    setIsMobileMenuOpen(false);\n  };\n  return /*#__PURE__*/_jsxDEV(\"nav\", {\n    className: \"modern-navbar\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"navbar-container\",\n      children: [/*#__PURE__*/_jsxDEV(Link, {\n        to: \"/\",\n        className: \"navbar-brand\",\n        onClick: closeMobileMenu,\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"brand-icon\",\n          children: \"\\uD83D\\uDCB0\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 20,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"brand-text\",\n          children: \"ExpenseTracker\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 21,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 19,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: `navbar-menu ${isMobileMenuOpen ? 'active' : ''}`,\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"navbar-nav\",\n          children: [/*#__PURE__*/_jsxDEV(Link, {\n            to: \"/\",\n            className: \"nav-link\",\n            onClick: closeMobileMenu,\n            children: \"Home\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 26,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Link, {\n            to: \"/about\",\n            className: \"nav-link\",\n            onClick: closeMobileMenu,\n            children: \"About\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 27,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Link, {\n            to: \"/contact\",\n            className: \"nav-link\",\n            onClick: closeMobileMenu,\n            children: \"Contact\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 28,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 25,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"navbar-auth\",\n          children: [/*#__PURE__*/_jsxDEV(Link, {\n            to: \"/login\",\n            className: \"auth-btn login-btn\",\n            onClick: closeMobileMenu,\n            children: \"Login\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 32,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Link, {\n            to: \"/signup\",\n            className: \"auth-btn signup-btn\",\n            onClick: closeMobileMenu,\n            children: \"Sign Up\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 35,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 31,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 24,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mobile-menu-toggle\",\n        onClick: toggleMobileMenu,\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          className: isMobileMenuOpen ? 'active' : ''\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 42,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          className: isMobileMenuOpen ? 'active' : ''\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 43,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          className: isMobileMenuOpen ? 'active' : ''\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 44,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 41,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 18,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 17,\n    columnNumber: 5\n  }, this);\n}\n_s(Navbar, \"QerECOS75+B7gv+k3q7FrDf39mc=\");\n_c = Navbar;\nexport default Navbar;\nvar _c;\n$RefreshReg$(_c, \"Navbar\");", "map": {"version": 3, "names": ["Link", "useState", "jsxDEV", "_jsxDEV", "<PERSON><PERSON><PERSON>", "_s", "isMobileMenuOpen", "setIsMobileMenuOpen", "toggleMobileMenu", "closeMobileMenu", "className", "children", "to", "onClick", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["D:/Expense Tracker App (MERN )/expensetracker/src/components/Navbar.js"], "sourcesContent": ["import { Link } from \"react-router-dom\";\r\nimport { useState } from \"react\";\r\nimport './Navbar.css';\r\n\r\nfunction Navbar() {\r\n  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);\r\n\r\n  const toggleMobileMenu = () => {\r\n    setIsMobileMenuOpen(!isMobileMenuOpen);\r\n  };\r\n\r\n  const closeMobileMenu = () => {\r\n    setIsMobileMenuOpen(false);\r\n  };\r\n\r\n  return (\r\n    <nav className=\"modern-navbar\">\r\n      <div className=\"navbar-container\">\r\n        <Link to=\"/\" className=\"navbar-brand\" onClick={closeMobileMenu}>\r\n          <span className=\"brand-icon\">💰</span>\r\n          <span className=\"brand-text\">ExpenseTracker</span>\r\n        </Link>\r\n\r\n        <div className={`navbar-menu ${isMobileMenuOpen ? 'active' : ''}`}>\r\n          <div className=\"navbar-nav\">\r\n            <Link to=\"/\" className=\"nav-link\" onClick={closeMobileMenu}>Home</Link>\r\n            <Link to=\"/about\" className=\"nav-link\" onClick={closeMobileMenu}>About</Link>\r\n            <Link to=\"/contact\" className=\"nav-link\" onClick={closeMobileMenu}>Contact</Link>\r\n          </div>\r\n\r\n          <div className=\"navbar-auth\">\r\n            <Link to=\"/login\" className=\"auth-btn login-btn\" onClick={closeMobileMenu}>\r\n              Login\r\n            </Link>\r\n            <Link to=\"/signup\" className=\"auth-btn signup-btn\" onClick={closeMobileMenu}>\r\n              Sign Up\r\n            </Link>\r\n          </div>\r\n        </div>\r\n\r\n        <div className=\"mobile-menu-toggle\" onClick={toggleMobileMenu}>\r\n          <span className={isMobileMenuOpen ? 'active' : ''}></span>\r\n          <span className={isMobileMenuOpen ? 'active' : ''}></span>\r\n          <span className={isMobileMenuOpen ? 'active' : ''}></span>\r\n        </div>\r\n      </div>\r\n    </nav>\r\n  );\r\n}\r\n\r\nexport default Navbar;\r\n"], "mappings": ";;AAAA,SAASA,IAAI,QAAQ,kBAAkB;AACvC,SAASC,QAAQ,QAAQ,OAAO;AAChC,OAAO,cAAc;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEtB,SAASC,MAAMA,CAAA,EAAG;EAAAC,EAAA;EAChB,MAAM,CAACC,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGN,QAAQ,CAAC,KAAK,CAAC;EAE/D,MAAMO,gBAAgB,GAAGA,CAAA,KAAM;IAC7BD,mBAAmB,CAAC,CAACD,gBAAgB,CAAC;EACxC,CAAC;EAED,MAAMG,eAAe,GAAGA,CAAA,KAAM;IAC5BF,mBAAmB,CAAC,KAAK,CAAC;EAC5B,CAAC;EAED,oBACEJ,OAAA;IAAKO,SAAS,EAAC,eAAe;IAAAC,QAAA,eAC5BR,OAAA;MAAKO,SAAS,EAAC,kBAAkB;MAAAC,QAAA,gBAC/BR,OAAA,CAACH,IAAI;QAACY,EAAE,EAAC,GAAG;QAACF,SAAS,EAAC,cAAc;QAACG,OAAO,EAAEJ,eAAgB;QAAAE,QAAA,gBAC7DR,OAAA;UAAMO,SAAS,EAAC,YAAY;UAAAC,QAAA,EAAC;QAAE;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACtCd,OAAA;UAAMO,SAAS,EAAC,YAAY;UAAAC,QAAA,EAAC;QAAc;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC9C,CAAC,eAEPd,OAAA;QAAKO,SAAS,EAAE,eAAeJ,gBAAgB,GAAG,QAAQ,GAAG,EAAE,EAAG;QAAAK,QAAA,gBAChER,OAAA;UAAKO,SAAS,EAAC,YAAY;UAAAC,QAAA,gBACzBR,OAAA,CAACH,IAAI;YAACY,EAAE,EAAC,GAAG;YAACF,SAAS,EAAC,UAAU;YAACG,OAAO,EAAEJ,eAAgB;YAAAE,QAAA,EAAC;UAAI;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACvEd,OAAA,CAACH,IAAI;YAACY,EAAE,EAAC,QAAQ;YAACF,SAAS,EAAC,UAAU;YAACG,OAAO,EAAEJ,eAAgB;YAAAE,QAAA,EAAC;UAAK;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAC7Ed,OAAA,CAACH,IAAI;YAACY,EAAE,EAAC,UAAU;YAACF,SAAS,EAAC,UAAU;YAACG,OAAO,EAAEJ,eAAgB;YAAAE,QAAA,EAAC;UAAO;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9E,CAAC,eAENd,OAAA;UAAKO,SAAS,EAAC,aAAa;UAAAC,QAAA,gBAC1BR,OAAA,CAACH,IAAI;YAACY,EAAE,EAAC,QAAQ;YAACF,SAAS,EAAC,oBAAoB;YAACG,OAAO,EAAEJ,eAAgB;YAAAE,QAAA,EAAC;UAE3E;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACPd,OAAA,CAACH,IAAI;YAACY,EAAE,EAAC,SAAS;YAACF,SAAS,EAAC,qBAAqB;YAACG,OAAO,EAAEJ,eAAgB;YAAAE,QAAA,EAAC;UAE7E;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENd,OAAA;QAAKO,SAAS,EAAC,oBAAoB;QAACG,OAAO,EAAEL,gBAAiB;QAAAG,QAAA,gBAC5DR,OAAA;UAAMO,SAAS,EAAEJ,gBAAgB,GAAG,QAAQ,GAAG;QAAG;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eAC1Dd,OAAA;UAAMO,SAAS,EAAEJ,gBAAgB,GAAG,QAAQ,GAAG;QAAG;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eAC1Dd,OAAA;UAAMO,SAAS,EAAEJ,gBAAgB,GAAG,QAAQ,GAAG;QAAG;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACvD,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV;AAACZ,EAAA,CA5CQD,MAAM;AAAAc,EAAA,GAANd,MAAM;AA8Cf,eAAeA,MAAM;AAAC,IAAAc,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}