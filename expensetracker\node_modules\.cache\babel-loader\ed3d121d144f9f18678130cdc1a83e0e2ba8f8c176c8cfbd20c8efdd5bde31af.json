{"ast": null, "code": "var _jsxFileName = \"D:\\\\Expense Tracker App (MERN )\\\\expensetracker\\\\src\\\\components\\\\home\\\\Features.js\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction Features() {\n  return /*#__PURE__*/_jsxDEV(\"section\", {\n    className: \"container py-5\",\n    children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n      className: \"text-center mb-4 text-info\",\n      children: \"Why Use Our Expense Tracker?\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 4,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"row text-center\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"col-md-4 mb-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"i\", {\n          className: \"bi bi-cash-coin display-4 text-success\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 7,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"h4\", {\n          className: \"mt-3\",\n          children: \"Track Expenses\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 8,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"Record your daily spendings in just a few clicks.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 9,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 6,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"col-md-4 mb-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"i\", {\n          className: \"bi bi-bar-chart-line display-4 text-warning\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 12,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"h4\", {\n          className: \"mt-3\",\n          children: \"Smart Analytics\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 13,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"Visualize your spending patterns & trends.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 14,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 11,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"col-md-4 mb-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"i\", {\n          className: \"bi bi-lock-fill display-4 text-danger\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 17,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"h4\", {\n          className: \"mt-3\",\n          children: \"100% Secure\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 18,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"Your data is encrypted and private.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 19,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 16,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 5,\n      columnNumber: 13\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 3,\n    columnNumber: 9\n  }, this);\n}\n_c = Features;\nexport default Features;\nvar _c;\n$RefreshReg$(_c, \"Features\");", "map": {"version": 3, "names": ["Features", "_jsxDEV", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["D:/Expense Tracker App (MERN )/expensetracker/src/components/home/<USER>"], "sourcesContent": ["function Features() {\r\n    return (\r\n        <section className=\"container py-5\">\r\n            <h2 className=\"text-center mb-4 text-info\">Why Use Our Expense Tracker?</h2>\r\n            <div className=\"row text-center\">\r\n                <div className=\"col-md-4 mb-4\">\r\n                    <i className=\"bi bi-cash-coin display-4 text-success\"></i>\r\n                    <h4 className=\"mt-3\">Track Expenses</h4>\r\n                    <p>Record your daily spendings in just a few clicks.</p>\r\n                </div>\r\n                <div className=\"col-md-4 mb-4\">\r\n                    <i className=\"bi bi-bar-chart-line display-4 text-warning\"></i>\r\n                    <h4 className=\"mt-3\">Smart Analytics</h4>\r\n                    <p>Visualize your spending patterns & trends.</p>\r\n                </div>\r\n                <div className=\"col-md-4 mb-4\">\r\n                    <i className=\"bi bi-lock-fill display-4 text-danger\"></i>\r\n                    <h4 className=\"mt-3\">100% Secure</h4>\r\n                    <p>Your data is encrypted and private.</p>\r\n                </div>\r\n            </div>\r\n        </section>\r\n    );\r\n}\r\nexport default Features;"], "mappings": ";;AAAA,SAASA,QAAQA,CAAA,EAAG;EAChB,oBACIC,OAAA;IAASC,SAAS,EAAC,gBAAgB;IAAAC,QAAA,gBAC/BF,OAAA;MAAIC,SAAS,EAAC,4BAA4B;MAAAC,QAAA,EAAC;IAA4B;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,eAC5EN,OAAA;MAAKC,SAAS,EAAC,iBAAiB;MAAAC,QAAA,gBAC5BF,OAAA;QAAKC,SAAS,EAAC,eAAe;QAAAC,QAAA,gBAC1BF,OAAA;UAAGC,SAAS,EAAC;QAAwC;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC1DN,OAAA;UAAIC,SAAS,EAAC,MAAM;UAAAC,QAAA,EAAC;QAAc;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACxCN,OAAA;UAAAE,QAAA,EAAG;QAAiD;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACvD,CAAC,eACNN,OAAA;QAAKC,SAAS,EAAC,eAAe;QAAAC,QAAA,gBAC1BF,OAAA;UAAGC,SAAS,EAAC;QAA6C;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC/DN,OAAA;UAAIC,SAAS,EAAC,MAAM;UAAAC,QAAA,EAAC;QAAe;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACzCN,OAAA;UAAAE,QAAA,EAAG;QAA0C;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAChD,CAAC,eACNN,OAAA;QAAKC,SAAS,EAAC,eAAe;QAAAC,QAAA,gBAC1BF,OAAA;UAAGC,SAAS,EAAC;QAAuC;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACzDN,OAAA;UAAIC,SAAS,EAAC,MAAM;UAAAC,QAAA,EAAC;QAAW;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACrCN,OAAA;UAAAE,QAAA,EAAG;QAAmC;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACzC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACD,CAAC;AAElB;AAACC,EAAA,GAvBQR,QAAQ;AAwBjB,eAAeA,QAAQ;AAAC,IAAAQ,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}